# Shuxiaoqi 搜索模块重构

## 项目概述

本次重构将搜索页升级为"主控制器+子控制器"架构。主控制器负责顶部搜索栏、分类Tab、输入框和子控制器切换。每个分类（视频、用户、商品、商家、团购、外卖）拥有独立的子控制器，分别管理各自的列表、筛选和分页逻辑，提升代码可维护性和扩展性。

## 目标用户

面向需要高效搜索多类型内容（如视频、用户、商品等）的终端用户，追求流畅体验和清晰分类。

## 技术选型

开发框架: UIKit + 容器VC
数据持久化: UserDefaults（历史记录）
状态管理: 原生状态管理
UI风格: 遵循iOS HIG，现代简约，冷色系霓虹渐变

## 应用结构

- SearchViewController（主控制器，顶部搜索栏+分类Tab+子控制器容器）
- 各分类子控制器（独立管理各自的列表、筛选、分页等）

## 页面结构

| 页面/视图名称                | 用途           | 核心功能                         | 技术实现         | 导航/用户流程         | 建议文件路径                       |
|:---------------------------:|:--------------:|:--------------------------------:|:----------------:|:---------------------:|:-----------------------------------:|
| SearchViewController        | 搜索主页面     | 搜索栏、分类Tab、输入框、子控制器 | UIKit + 容器VC   | 入口，切换子VC        | Shuxiaoqi/Modules/Controller/Main/SearchViewController.swift |
| SearchVideoViewController   | 视频搜索列表   | 视频结果、筛选、分页、刷新        | UIKit + UICollectionView | Tab切换/输入搜索 | Shuxiaoqi/Modules/Controller/Search/SearchVideoViewController.swift |
| SearchUserViewController    | 用户搜索列表   | 用户结果、筛选、分页、刷新        | UIKit + UITableView | Tab切换/输入搜索 | Shuxiaoqi/Modules/Controller/Search/SearchUserViewController.swift |
| SearchProductViewController | 商品搜索列表   | 商品结果、筛选、分页、刷新        | UIKit + UITableView | Tab切换/输入搜索 | Shuxiaoqi/Modules/Controller/Search/SearchProductViewController.swift |
| SearchMerchantViewController| 商家搜索列表   | 商家结果、筛选、分页、刷新        | UIKit + UITableView | Tab切换/输入搜索 | Shuxiaoqi/Modules/Controller/Search/SearchMerchantViewController.swift |
| SearchGroupBuyViewController| 团购搜索列表   | 团购结果、筛选、分页、刷新        | UIKit + UITableView | Tab切换/输入搜索 | Shuxiaoqi/Modules/Controller/Search/SearchGroupBuyViewController.swift |
| SearchTakeoutViewController | 外卖搜索列表   | 外卖结果、筛选、分页、刷新        | UIKit + UITableView | Tab切换/输入搜索 | Shuxiaoqi/Modules/Controller/Search/SearchTakeoutViewController.swift |

## 数据模型

- 搜索历史：UserDefaults存储字符串数组
- 各分类结果：根据API返回结构自定义

## 技术实现细节

### 评论操作弹窗（CommentActionSheet）

#### UI设计方案
- 在评论cell（一级/二级）添加长按手势，弹出自定义操作弹窗。
- 弹窗背景色#EFEFEF，顶部有#C4C4C4横线，圆角处理。
- 每个操作为一行，cell背景白色，左右间距8pt，图标和文字左对齐。
- 图标资源名：
  - 回复：comment_action_reply
  - 复制：comment_action_copy
  - 举报：comment_action_report
  - 删除：comment_action_delete（红色，仅本人评论显示）
- 弹窗底部安全区适配，支持点击空白处关闭。

#### 数据管理方案
- 通过传入评论模型判断当前评论是否为本人（model.customerId == 当前用户ID）。
- 弹窗操作通过闭包回调传递到VC，分别处理回复、复制、举报、删除逻辑。

#### 交互实现
- 长按评论cell弹出操作弹窗，点击操作后自动关闭弹窗并执行对应操作。
- 复制操作调用系统粘贴板，举报/删除弹出确认或直接调用API。

#### iOS特性利用
- 使用自定义UIView实现弹窗，保证自定义UI风格。
- 复制功能用UIPasteboard，举报/删除用现有API。

#### 可访问性考虑
- 所有按钮支持VoiceOver描述。
- 颜色对比度符合无障碍标准。

#### 组件复用
- 弹窗为独立组件CommentActionSheet，可在一级/二级评论中复用。

## 开发状态跟踪

| 页面/组件名称                | 开发状态 | 文件路径 |
|:---------------------------:|:--------:|:-----------------------------------:|
| SearchViewController        | 已完成   | Shuxiaoqi/Modules/Controller/Main/SearchViewController.swift |
| SearchVideoViewController   | 已完成   | Shuxiaoqi/Modules/Controller/Search/SearchVideoViewController.swift |
| SearchUserViewController    | 未开始   | Shuxiaoqi/Modules/Controller/Search/SearchUserViewController.swift |
| SearchProductViewController | 未开始   | Shuxiaoqi/Modules/Controller/Search/SearchProductViewController.swift |
| SearchMerchantViewController| 未开始   | Shuxiaoqi/Modules/Controller/Search/SearchMerchantViewController.swift |
| SearchGroupBuyViewController| 未开始   | Shuxiaoqi/Modules/Controller/Search/SearchGroupBuyViewController.swift |
| SearchTakeoutViewController | 未开始   | Shuxiaoqi/Modules/Controller/Search/SearchTakeoutViewController.swift |
| 评论操作弹窗 | 进行中 | Shuxiaoqi/Modules/Controller/Video/Player/CommentActionSheet.swift |
| InfoPopupView | 已完成 | Shuxiaoqi/Components/InfoPopupView.swift |

## 技术实现细节
### 个人中心页面（MeViewController）

#### 技术方案更新

- 顶部个人信息区域已由原ProfileCell（UITableViewCell）重构为ProfileHeaderView（UIView），作为tableView.tableHeaderView使用。
- 这样结构更优雅，便于实现下拉放大、渐变导航栏等主流交互，代码更清晰。
- ProfileHeaderView通过闭包回调与MeViewController解耦，支持头像、关注、粉丝等点击事件。
- 数据流、动效、适配等均已适配新结构。
- ProfileCell已废弃，如需内容cell可重构，否则可安全删除。

#### 主要实现特点
- 头部信息与内容分离，维护和扩展更方便
- tableHeaderView高度自适应，支持下拉放大动效
- 事件回调解耦，便于后续扩展
- 代码结构更贴合iOS主流App个人中心实现 

### 修复：TabBar页面重复响应通知导致重复跳转问题

#### 问题描述
在HomeViewController和MeViewController中，均注册了NavigateToViewControllerNotification通知，导致当侧边菜单发出跳转通知时，两个页面会同时响应，出现重复push/present的bug。

#### 解决方案
- 在HomeViewController和MeViewController的navigateToViewController(_:)方法中，增加判断：只有当前TabBarController的selectedViewController（即当前显示的页面）才会响应通知，其他页面忽略。
- 这样可彻底避免重复push/present，保证跳转逻辑唯一。
- 在LeftMenuViewController的相关方法中增加注释，说明此机制。

#### 关键代码示例
```swift
@objc func navigateToViewController(_ notification: Notification) {
    guard let tabBarController = self.tabBarController else { return }
    let isCurrentTab: Bool
    if let nav = self.navigationController {
        isCurrentTab = (tabBarController.selectedViewController === nav)
    } else {
        isCurrentTab = (tabBarController.selectedViewController === self)
    }
    guard isCurrentTab else { return }
    // ... 执行push/present ...
}
```

#### 影响范围
- HomeViewController
- MeViewController
- LeftMenuViewController（注释说明）

#### 修复效果
- 彻底解决了TabBar多页面重复响应通知导致的重复跳转问题。
- 跳转逻辑更安全、健壮。 

### 朋友页面（FriendViewController）

本次调整将 `VideoDisplayCenterViewController` 作为子控制器嵌入时的底部偏移逻辑前移至 `FriendViewController`，统一由父级控制。

- **底部约束计算**：在 `setupVideoDisplay()` 中根据 `CustomTabBarController` 的显示状态和当前 `safeAreaInsets.bottom` 动态计算 `tabBarHeight = 44 + safeAreaInsets.bottom`。
- **SnapKit 约束**：对子控制器视图使用 `top/left/right` 与父视图对齐，`bottom` 约束为 `-tabBarHeight`，并记录 `videoDisplayBottomConstraint` 以便在 `viewSafeAreaInsetsDidChange` 中更新。
- **动态更新**：当安全区或 TabBar 显隐发生变化时，通过 `updateVideoDisplayBottomConstraint()` 更新底部偏移，确保布局始终准确。
- **子控制器配置**：将 `needsTabBarOffset` 设为 `false`，以避免 `VideoDisplayCenterViewController` 内部重复处理偏移。

该调整消除了在 `VideoDisplayCenterViewController` 内部对 TabBar 高度的依赖，父级控制器逻辑更加集中，降低耦合。

## 发布模块

### 页面结构

| 页面/视图名称 | 用途 | 核心功能 | 技术实现 | 导航/用户流程 | 建议文件路径 |
|:-------------:|:----:|:--------:|:--------:|:-------------:|:-------------:|
| NoteEditingDetailsViewController | 笔记(图片)发布页 | 图片选择九宫格、标题、分类、权限、发布等 | UIKit + SnapKit | 从主入口跳转 | Shuxiaoqi/Modules/Controller/Note/NoteEditingDetailsViewController.swift |
| VideoEditingDetailsViewController | 视频发布页 | 视频封面、标题、分类、权限、发布等 | UIKit + SnapKit | 视频录制后跳转 | Shuxiaoqi/Modules/Controller/Video/Upload/VideoEditingDetailsViewController.swift |
| PlayerTestVC | 已完成 | Shuxiaoqi/Modules/Controller/Video/Player/PlayerTestVC.swift |

### 技术实现细节

#### NoteEditingDetailsViewController (已重构为独立页面)

##### UI设计方案
- 页面顶部为 UICollectionView 九宫格，用于显示和管理待上传的图片。
- 页面中部为标题输入框和一系列设置项（分类、谁可以看、评论开关等）。
- 所有内容包含在一个 UIScrollView 中，以适配小屏幕。

##### 数据管理方案
- 使用 `[UIImage]` 数组管理选中的图片。
- 使用 `UITextViewDelegate` 管理标题的输入和字数限制。
- 各项设置（如分类ID、权限索引）通过属性直接存储。

##### 交互实现
- 最多选择9张图片，超出部分"添加"按钮自动隐藏。
- 点击图片可预览，预览页支持删除。
- 点击发布按钮时，统一校验所有输入（图片、标题、分类等）。

##### 组件复用
- (无) - 已与 `BasePublishDetailsViewController` 解耦，成为独立模块。

#### 功能完整性检查表

- [x] 图片九宫格展示  
- [x] 图片选择 / 删除
- [x] 标题输入与字数限制
- [x] 分类、权限等设置项
- [x] 发布前参数校验

### 开发状态跟踪（新增）
| 页面/组件名称 | 开发状态 | 文件路径 |
|:-------------:|:--------:|:-------------:|
| NoteEditingDetailsViewController | 已完成 | Shuxiaoqi/Modules/Controller/Note/NoteEditingDetailsViewController.swift |
| VideoEditingDetailsViewController | 进行中 | Shuxiaoqi/Modules/Controller/Video/Upload/VideoEditingDetailsViewController.swift |
| PlayerTestVC | 已完成 | Shuxiaoqi/Modules/Controller/Video/Player/PlayerTestVC.swift |
| NoteCarouselView | 已完成 | Shuxiaoqi/Modules/Controller/Video/Player/NoteCarouselView.swift |

## 技术实现细节
### PlayerTestVC（播放器SDK测试页）

#### UI设计方案
- 竖屏9:16比例播放器区域，居中显示。
- 中央有播放/暂停切换按钮，左上角有返回按钮。
- 主题为暗色科技风，按钮采用冷色系渐变。
- 仅支持竖屏显示。

#### 数据管理方案
- 预留播放器SDK实例属性，后续集成腾讯云点播播放器。
- 播放/暂停状态由本地变量管理。

#### 交互实现
- 点击播放/暂停按钮切换视频播放状态。
- 点击返回按钮返回上一级页面。

#### iOS特性利用
- 强制竖屏，禁止横屏切换。
- 适配安全区，支持不同设备尺寸。

#### 可访问性考虑
- 按钮支持VoiceOver描述。
- 按钮尺寸适配动态字体。

#### 组件复用
- 暂无，后续可将播放器封装为独立组件。

#### 功能完整性检查表
- [x] 竖屏9:16播放器区域
- [x] 播放/暂停切换按钮
- [x] 返回按钮
- [x] 入口集成到消息中心
- [ ] 腾讯云播放器SDK集成
- [ ] 点播视频播放 

### 视频播放器模块（短视频中心页）

#### 架构概览
播放器模块围绕`VideoDisplayCenterViewController`构建，采用**VC + 纯视图组件**分离思路：

1. `VideoDisplayCenterViewController`：
   - 负责视频流数据获取、页面缓存、播放器生命周期管理。
   - 内部嵌入垂直方向 `UIPageViewController`，实现抖音式上下滑播放。
2. `VideoDisplayCenterUIComponents`：
   - 组装自定义导航栏及 `UIPageViewController`，仅处理布局，不含业务逻辑。
3. `VideoPage`：
   - 每条短视频对应的子控制器，管理一个 `TXVodPlayer` 实例。
   - 通过 `VideoPageUIComponents` 构建 UI，监听播放事件，通过 `VideoPlayerDelegate` 与 SDK 解耦。
4. `VideoPageUIComponents`：
   - 集中管理播放器视图、交互按钮、进度条、作者信息等 UI 元素。
   - 通过 `VideoProgressDragDelegate` 回调拖拽事件，提升代码可读性。
5. `UIViewExtensions`：
   - 提供渐变边框、渐变背景等视觉工具方法，保持品牌色一致。
6. `VideoDisplayCenterViewProtocols`：
   - 定义 `VideoPageDelegate` 等协议，承担 VC 与 Page 之间事件传递，降低耦合度。

#### 页面滑动 & 资源管理
- **缓存策略**：始终保留当前页 `±2` 的 `VideoPage`，滑到第 N 页时释放 N-2 之前的播放器资源；确保内存占用稳定。
- **播放器初始化**：`VideoPage.initializePlayerIfNeeded()` 避免重复创建，同一个页面仅持有 1 个 `TXVodPlayer`。
- **错误降级**：若播放参数缺失，展示错误占位视图，页面依旧可正常滑动。
- **分享流程**：`VideoPage` 触发 `videoPageDidTapShare` 事件，由父级 VC 调用 `showShareSheet()` 统一弹出系统分享。

#### 交互细节
- 主题色遵循冷色系霓虹渐变，通过 `themeGradientColors`（橙→红）实现关注按钮和头像环形描边。
- 进度条支持点击跳转 + 拖动快速定位，自动展示时间提示与光晕动画反馈。
- 页面切换时暂停上一页播放器，进入新页后自动从头播放；保证观感连贯。

#### 依赖 & SDK
- **TXLiteAVSDK_UGC**：腾讯云点播播放器，`TXVodPlayer` 作为核心播放组件。
- **SnapKit**：AutoLayout DSL，用于所有 UI 组件快速布局。

> 如需在其他场景复用播放器，可直接组合 `VideoPage` 与自定义容器 VC，或参考 `VideoPageUIComponents` 封装更细粒度的 UI 组件。 

### CreativeCenterViewController（创作中心）统计信息弹窗交互

- 点击【发布数】【总播放量】【获赞数】【粉丝数】四个统计视图，统一调用复用组件 `InfoPopupView` 弹出提示。
    - 发布数：`当前共发布xx个作品`
    - 总播放量：`当前共获得xx播放量`（≥1万按 `%.1f万` 格式）
    - 获赞数：`共获得xx点赞`（≥1万按 `%.1f万` 格式）
    - 粉丝数：点击跳转到粉丝列表（与个人中心一致）
- 统计数值实时读取 `currentUserStats` 缓存，确保与页面展示一致。
- 依赖 `InfoPopupView.show(in:title:message:)`，保持视觉和交互一致性。 

### DeviceUtils（获取用户自定义设备名称）

> 新增时间：2025/07/25

#### 作用
- 统一获取用户在“设置→通用→关于本机→名称”中自定义的设备名称。
- 在真机上优先通过 `gethostname` 获取，可拿到不带空格及特殊字符的设备名称；如失败则回退到 `UIDevice.current.name`。

#### 关键实现
```swift
struct DeviceUtils {
    static func deviceCustomName() -> String {
        var buffer = [CChar](repeating: 0, count: 256)
        if gethostname(&buffer, buffer.count) == 0 {
            let hostName = String(cString: buffer)
            if !hostName.isEmpty && !isGenericHostName(hostName) {
                return hostName
            }
        }
        return sanitize(UIDevice.current.name)
    }
    static func marketingDeviceModel() -> String { /* 由硬件标识符映射市场名称，如 iPhone12,1 → iPhone 11 */ }
    private static func isGenericHostName(_ name: String) -> Bool { /* ... */ }
    private static func sanitize(_ name: String) -> String { /* ... */ }
}
```

#### 集成位置
- 代码文件：`Shuxiaoqi/Utilities/DeviceUtils.swift`
- 已在 `AppDelegate` 与 `LoginViewModel` 中替换旧逻辑，后续如需获取设备名称，均调用 `DeviceUtils.deviceCustomName()` 以保持一致。

#### 更新影响
- 去除对 `TVCUtils.tvc_deviceModelName()` + `UIDevice.current.name` 的拼接逻辑，避免获取到设备型号而非自定义名称。
- 若需硬件型号，请继续使用 `TVCUtils.tvc_deviceModelName()`。 