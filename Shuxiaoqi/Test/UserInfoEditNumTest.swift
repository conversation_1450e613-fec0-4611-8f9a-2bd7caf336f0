//
//  UserInfoEditNumTest.swift
//  Shuxiaoqi
//
//  Created by AI Assistant on 2025/7/25.
//  Test file to verify UserInfoEditNumViewController validation logic
//

import UIKit

class UserInfoEditNumTestViewController: UIViewController {
    
    private lazy var scrollView: UIScrollView = {
        let scroll = UIScrollView()
        scroll.backgroundColor = UIColor(hex: "#F5F5F5")
        return scroll
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "树小柒号验证测试"
        label.font = UIFont.boldSystemFont(ofSize: 18)
        label.textColor = UIColor(hex: "#333333")
        label.textAlignment = .center
        return label
    }()
    
    private lazy var testCasesStackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 15
        stack.distribution = .fill
        return stack
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "树小柒号验证测试"
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        
        setupUI()
        setupTestCases()
    }
    
    private func setupUI() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubview(titleLabel)
        contentView.addSubview(testCasesStackView)
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.right.equalToSuperview().inset(20)
        }
        
        testCasesStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(30)
            make.left.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func setupTestCases() {
        let testCases = [
            ("测试1: 只有数字", "123456", "至少包含一个英文字母"),
            ("测试2: 太短", "abc", "至少需要6个字符"),
            ("测试3: 太长", "abcdefghijklmnopqrstuvwxyz", "最多只能输入15个字符"),
            ("测试4: 包含特殊字符", "abc@123", "只能包含英文字母、数字和下划线"),
            ("测试5: 有效输入", "abc123", "验证通过"),
            ("测试6: 包含下划线", "abc_123", "验证通过"),
            ("测试7: 全英文", "abcdef", "验证通过"),
            ("测试8: 英文+数字+下划线", "user_123", "验证通过")
        ]
        
        for (title, input, expectedResult) in testCases {
            let testView = createTestCaseView(title: title, input: input, expectedResult: expectedResult)
            testCasesStackView.addArrangedSubview(testView)
        }
    }
    
    private func createTestCaseView(title: String, input: String, expectedResult: String) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 8
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 1)
        containerView.layer.shadowOpacity = 0.1
        containerView.layer.shadowRadius = 2
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.boldSystemFont(ofSize: 14)
        titleLabel.textColor = UIColor(hex: "#333333")
        
        let inputLabel = UILabel()
        inputLabel.text = "输入: \(input)"
        inputLabel.font = UIFont.systemFont(ofSize: 12)
        inputLabel.textColor = UIColor(hex: "#666666")
        
        let expectedLabel = UILabel()
        expectedLabel.text = "期望: \(expectedResult)"
        expectedLabel.font = UIFont.systemFont(ofSize: 12)
        expectedLabel.textColor = UIColor(hex: "#666666")
        
        let testButton = UIButton(type: .system)
        testButton.setTitle("测试", for: .normal)
        testButton.setTitleColor(.white, for: .normal)
        testButton.backgroundColor = UIColor(hex: "#FF6236")
        testButton.layer.cornerRadius = 4
        testButton.titleLabel?.font = UIFont.systemFont(ofSize: 12)
        
        let resultLabel = UILabel()
        resultLabel.text = "结果: 未测试"
        resultLabel.font = UIFont.systemFont(ofSize: 12)
        resultLabel.textColor = UIColor(hex: "#999999")
        resultLabel.numberOfLines = 0
        
        containerView.addSubview(titleLabel)
        containerView.addSubview(inputLabel)
        containerView.addSubview(expectedLabel)
        containerView.addSubview(testButton)
        containerView.addSubview(resultLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
        }
        
        inputLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.left.right.equalTo(titleLabel)
        }
        
        expectedLabel.snp.makeConstraints { make in
            make.top.equalTo(inputLabel.snp.bottom).offset(4)
            make.left.right.equalTo(titleLabel)
        }
        
        testButton.snp.makeConstraints { make in
            make.top.equalTo(expectedLabel.snp.bottom).offset(8)
            make.left.equalTo(titleLabel)
            make.width.equalTo(60)
            make.height.equalTo(30)
        }
        
        resultLabel.snp.makeConstraints { make in
            make.top.equalTo(testButton.snp.bottom).offset(8)
            make.left.right.equalTo(titleLabel)
            make.bottom.equalToSuperview().offset(-12)
        }
        
        // 添加测试按钮点击事件
        testButton.addTarget(self, action: #selector(runTest(_:)), for: .touchUpInside)
        testButton.tag = input.hashValue // 使用输入字符串的hash作为tag来识别
        
        // 存储相关信息到button的accessibilityLabel中
        testButton.accessibilityLabel = "\(input)|\(expectedResult)"
        
        return containerView
    }
    
    @objc private func runTest(_ sender: UIButton) {
        guard let info = sender.accessibilityLabel?.components(separatedBy: "|"),
              info.count == 2 else { return }
        
        let input = info[0]
        let expectedResult = info[1]
        
        // 创建一个临时的UserInfoEditNumViewController来测试验证逻辑
        let testController = UserInfoEditNumViewController()
        testController.currentNum = "oldnum123" // 设置一个不同的原始值
        
        // 通过反射获取私有方法进行测试
        let result = testValidation(input: input, controller: testController)
        
        // 找到结果标签并更新
        if let containerView = sender.superview,
           let resultLabel = containerView.subviews.last as? UILabel {
            
            let isCorrect = result == expectedResult || 
                           (expectedResult == "验证通过" && result == "验证通过")
            
            resultLabel.text = "结果: \(result)"
            resultLabel.textColor = isCorrect ? UIColor(hex: "#4CAF50") : UIColor(hex: "#F44336")
            
            if !isCorrect {
                resultLabel.text += "\n❌ 与期望不符"
            } else {
                resultLabel.text += "\n✅ 符合期望"
            }
        }
    }
    
    private func testValidation(input: String, controller: UserInfoEditNumViewController) -> String {
        // 模拟验证逻辑
        let minLength = 6
        let maxLength = 15
        
        // 检查长度
        if input.count < minLength {
            return "至少需要\(minLength)个字符"
        }
        
        if input.count > maxLength {
            return "最多只能输入\(maxLength)个字符"
        }
        
        // 检查是否只包含允许的字符：英文字母、数字、下划线
        let allowedCharacterSet = CharacterSet.alphanumerics.union(CharacterSet(charactersIn: "_"))
        if input.rangeOfCharacter(from: allowedCharacterSet.inverted) != nil {
            return "只能包含英文字母、数字和下划线"
        }
        
        // 检查是否包含至少一个英文字母（必须）
        let letterCharacterSet = CharacterSet.letters
        if input.rangeOfCharacter(from: letterCharacterSet) == nil {
            return "至少包含一个英文字母"
        }
        
        return "验证通过"
    }
}
