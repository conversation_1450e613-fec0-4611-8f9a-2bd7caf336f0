//
//  UserInfoResponse.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/4/24.
//

import SmartCodable

struct UserInfoResponse: SmartCodable {
    var status: Int = 0 // 或者 Int = -1 作为默认值
    var errMsg: String?
    var msg: String?
    var data: UserInfoData?

    // HandyJSON 需要一个空的初始化器
    init() {}

    // 可选：添加一个计算属性来判断是否成功，类似 LoginResponse
    var isSuccess: Bool {
        return status == 200
    }

    // 可选：添加一个显示消息的计算属性
    var displayMessage: String {
        if let message = msg, !message.isEmpty {
            return message
        }
        if let errorMsg = errMsg, !errorMsg.isEmpty {
            return errorMsg
        }
        return isSuccess ? "成功" : "未知错误"
    }
}

struct UserInfoData: SmartCodable {
    var authName: String? = nil
    var customerAccount: String = ""
    var fansNumber: Int = 0
    var followNumber: Int = 0
    var likeNumber: Int = 0
    var nickName: String = ""
    // 个人简介 snapshot 字符串（兼容旧逻辑）
//    var personalitySign: String? = nil
    // 新接口返回的完整个性签名对象
    var personalitySign: PersonalitySign? = nil
    // 兴趣/职业标签
    var labels: [String] = []
    var customerId: String = ""

    // 新增：后台返回的背景图
    var backgroundImage: String? = nil
    var watchNumber: Int = 0
    var worksNumber: Int = 0
    var wxAvator: String = ""
    var customerName: String? = nil // 修改为可选类型，并提供默认值

    // 计算属性：获取用户昵称，兼容新旧数据结构
    var displayNickName: String {
        // 如果 nickName 不为空，直接返回
        if !nickName.isEmpty {
            return nickName
        }
        // 如果为空，返回默认值
        return "用户昵称"
    }
}
