//
//  UserInfoEditNumViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/5/15.
//

import Foundation
import UIKit

class UserInfoEditNumViewController: BaseViewController {
    
    // MARK: - 属性
    
    var currentNum: String = ""
    var maxLength: Int = 15
    var minLength: Int = 6
    var onNumUpdated: ((String) -> Void)?
    
    // MARK: - UI组件
    
    private lazy var numTextField: UITextField = {
        let textField = UITextField()
        textField.font = UIFont.systemFont(ofSize: 14)
        textField.textColor = UIColor(hex: "#333333")
        textField.returnKeyType = .done
        textField.clearButtonMode = .whileEditing
        textField.placeholder = "请输入树小柒号"
        textField.keyboardType = .asciiCapable // 英文+数字键盘
        textField.delegate = self
        textField.addTarget(self, action: #selector(textFieldDidChange), for: .editingChanged)
        return textField
    }()
    
    private lazy var textFieldContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 5
        return view
    }()
    
    private lazy var countLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#999999")
        label.textAlignment = .right
        return label
    }()
    
    private lazy var infoLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor(hex: "#777777")
        label.text = "请设置\(minLength)-\(maxLength)个字符，仅可使用英文（必须）、数字、下划线。树小柒号是账号的唯一凭证，只能修改一次。"
        label.numberOfLines = 0
        return label
    }()
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configureBaseSettings()
        setupUI()
        configureInitialData()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        numTextField.becomeFirstResponder()
        validateInput()
    }
    
    // MARK: - 设置方法
    
    private func configureBaseSettings() {
        // 设置导航栏标题
        navTitle = "编辑树小柒号"
        navBar.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 显示返回按钮
        showBackButton = true
        
        // 设置背景色
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        contentView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 添加确认按钮
        rightNavTitle = "确认"
        rightNavAction = #selector(confirmButtonTapped)
        rightNavButtonTintColor = UIColor(hex: "#FF6236")
    }
    
    private func setupUI() {
        // 添加输入框容器
        contentView.addSubview(textFieldContainer)
        textFieldContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(25)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.height.equalTo(40)
        }
        
        // 首先添加字数统计标签到容器中
        textFieldContainer.addSubview(countLabel)
        countLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(50)
        }
        
        // 然后添加输入框到容器中
        textFieldContainer.addSubview(numTextField)
        numTextField.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.right.equalTo(countLabel.snp.left).offset(-8)
            make.height.equalTo(40)
        }
        
        // 添加提示信息
        contentView.addSubview(infoLabel)
        infoLabel.snp.makeConstraints { make in
            make.top.equalTo(textFieldContainer.snp.bottom).offset(10)
            make.left.equalToSuperview().offset(32)
            make.right.equalToSuperview().offset(-32)
        }
    }
    
    private func configureInitialData() {
        numTextField.text = currentNum
        updateCountLabel()
        validateInput()
    }
    
    // MARK: - 事件处理
    
    @objc private func textFieldDidChange() {
        updateCountLabel()
        validateInput()
    }
    
    @objc private func confirmButtonTapped() {
        guard let num = numTextField.text, !num.isEmpty else {
            return
        }

        // 进行本地验证
        if !isValidNum(num) {
            // 根据具体的验证失败原因显示不同的HUD提示
            let errorMessage = getValidationErrorMessage(for: num)
            showToast(errorMessage)
            return
        }

        // 检查是否有修改
        if !hasChanged(num) {
            showToast("请修改后再提交")
            return
        }

        // 通过所有验证，执行后续步骤
        // 回调更新的树小柒号
        onNumUpdated?(num)

        // 返回上一页
        navigationController?.popViewController(animated: true)
    }
    
    // MARK: - 辅助方法
    
    private func updateCountLabel() {
        let currentCount = numTextField.text?.count ?? 0
        countLabel.text = "\(currentCount)/\(maxLength)"
    }
    
    private func validateInput(with text: String? = nil) {
        let input = text ?? numTextField.text ?? ""
        // 检查输入是否有效且与原始号码不同
        let isValid = isValidNum(input) && hasChanged(input)
        updateButtonAppearance(isEnabled: isValid)
    }
    
    private func updateButtonAppearance(isEnabled: Bool) {
        // 设置按钮状态
        rightNavButton?.isEnabled = isEnabled
        
        // 设置按钮颜色 - 无论是否可点击，颜色都是#FF6236
        rightNavButton?.setTitleColor(UIColor(hex: "#FF6236"), for: .normal)
        
        // 可以通过alpha值来视觉上区分是否可点击
        rightNavButton?.alpha = isEnabled ? 1.0 : 0.5
    }
    
    private func isValidNum(_ num: String) -> Bool {
        // 检查长度
        guard num.count >= minLength && num.count <= maxLength else {
            return false
        }
        
        // 检查是否只包含允许的字符：英文字母、数字、下划线
        let allowedCharacterSet = CharacterSet.alphanumerics.union(CharacterSet(charactersIn: "_"))
        guard num.rangeOfCharacter(from: allowedCharacterSet.inverted) == nil else {
            return false
        }
        
        // 检查是否包含至少一个英文字母（必须）
        let letterCharacterSet = CharacterSet.letters
        guard num.rangeOfCharacter(from: letterCharacterSet) != nil else {
            return false
        }
        
        return true
    }
    
    private func hasChanged(_ num: String) -> Bool {
        // 检查输入的号码是否与原始号码不同
        return num != currentNum
    }

    private func getValidationErrorMessage(for num: String) -> String {
        // 检查长度
        if num.count < minLength {
            return "至少需要\(minLength)个字符"
        }

        if num.count > maxLength {
            return "最多只能输入\(maxLength)个字符"
        }

        // 检查是否只包含允许的字符：英文字母、数字、下划线
        let allowedCharacterSet = CharacterSet.alphanumerics.union(CharacterSet(charactersIn: "_"))
        if num.rangeOfCharacter(from: allowedCharacterSet.inverted) != nil {
            return "只能包含英文字母、数字和下划线"
        }

        // 检查是否包含至少一个英文字母（必须）
        let letterCharacterSet = CharacterSet.letters
        if num.rangeOfCharacter(from: letterCharacterSet) == nil {
            return "至少包含一个英文字母"
        }

        // 如果所有验证都通过，返回通用错误消息
        return "请设置\(minLength)-\(maxLength)个有效字符，必须包含英文字母"
    }
}

// MARK: - UITextFieldDelegate

extension UserInfoEditNumViewController: UITextFieldDelegate {
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        // 限制最大输入长度
        guard let text = textField.text else { return true }
        let newLength = text.count + string.count - range.length
        if newLength > maxLength {
            return false
        }
        // 检查输入字符是否有效（只允许英文字母、数字和下划线）
        if !string.isEmpty {
            let allowedCharacters = CharacterSet.alphanumerics.union(CharacterSet(charactersIn: "_"))
            let characterSet = CharacterSet(charactersIn: string)
            if !allowedCharacters.isSuperset(of: characterSet) {
                return false
            }
        }
        // 计算新内容并提前校验，保证按钮状态及时更新
        if let textRange = Range(range, in: text) {
            let updatedText = text.replacingCharacters(in: textRange, with: string)
            // 更新countLabel
            countLabel.text = "\(updatedText.count)/\(maxLength)"
            // 校验输入
            validateInput(with: updatedText)
        }
        return true
    }
    
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        textField.resignFirstResponder()
        if isValidNum(textField.text ?? "") && hasChanged(textField.text ?? "") {
            confirmButtonTapped()
        }
        return true
    }
}
