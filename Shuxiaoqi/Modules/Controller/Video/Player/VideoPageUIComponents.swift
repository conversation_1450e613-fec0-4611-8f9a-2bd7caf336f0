//
//  VideoPageUIComponents.swift
//  Shuxiaoqi
//
//  Created by yong<PERSON>ng ye on 2025/3/26.
//

/**
 `VideoPageUIComponents`
 
 为单个短视频页面 (`VideoPage`) 提供完整 UI 组件集合。
 主要覆盖：
 - 播放器承载视图 `videoPlayerView` 与暂停按钮
 - 底部交互栏（评论输入框 + 点赞/收藏/分享/评论数按钮）
 - 用户信息（头像、昵称、关注按钮）及作品标题
 - 进度条（支持拖拽/点击，缓冲显示、时间提示）
 
 设计原则：
 1. **纯视图层** —— 不直接持有业务状态，通过方法更新状态，避免与 VC 产生循环引用。
 2. **主题一致** —— 通过 `themeGradientColors` 维护统一品牌渐变色，Avatar 边框、关注按钮等共享。
 3. **交互协议** —— 通过 `VideoProgressDragDelegate` 回调进度条拖拽事件，由 `VideoPage` 实现。
 4. **可配置布局** —— 通过父 VC 提供的 `needsTabBarOffset` 决定底部安全区偏移，适配多场景嵌入。
 */
import UIKit
import SnapKit

// MARK: - 视频页面UI组件
class VideoPageUIComponents {
    
    // MARK: - Theme Colors
    let themeGradientColors = [
        UIColor(hex: "#FF8C29"),
        UIColor(hex: "#FF5900")
    ]
    
    // MARK: - UI Components
    let mainContentView: UIView = {
        let view = UIView()
        view.backgroundColor = .black
        return view
    }()
    
    let videoProgressView: UIView = {
        // 创建普通UIView，后续在setupUI中将其替换为VideoProgressView
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        // 增加点击区域
        view.layer.cornerRadius = 1
        return view
    }()
    
    // 进度条缓冲层
    private let progressBuffer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.3)
        return view
    }()
    
    // 进度条前景
    private let progressForeground: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.6)
        return view
    }()
    
    // 进度条拖动指示器（滑块）
    private let progressHandle: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 6 // 调整圆角半径
        view.isHidden = false // 始终显示滑块
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 1)
        view.layer.shadowOpacity = 0.3
        view.layer.shadowRadius = 2
        // 添加边框
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.white.cgColor
        return view
    }()
    
    // 时间提示标签容器（显示在视频上层）
    private let timeLabelContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.9) // 增加不透明度
        view.layer.cornerRadius = 10 // 增加圆角
        view.clipsToBounds = true
        view.isHidden = true // 默认隐藏，拖动时显示
        view.alpha = 0.0 // 初始透明度为0
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.white.withAlphaComponent(0.5).cgColor // 增加边框透明度
        // 添加阴影效果
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowOpacity = 0.3
        view.layer.shadowRadius = 4
        return view
    }()

    // 时间提示标签
    private let timeLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 18, weight: .semibold) // 增大字体并加粗
        label.textAlignment = .center
        label.text = "00:00 / 00:00"
        label.backgroundColor = UIColor.clear
        return label
    }()
    
    // 进度条宽度约束
    private var progressWidthConstraint: NSLayoutConstraint?
    // 缓冲进度条宽度约束
    private var bufferWidthConstraint: NSLayoutConstraint?
    
    // 拖动相关属性
    private var isDragging = false
    private var videoDuration: Float = 0
    private weak var dragDelegate: VideoProgressDragDelegate?
    
    let videoBottomBar: UIView = {
        let view = UIView()
        view.backgroundColor = .black
        return view
    }()
    
    let commentInputView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#1A1A1A")
        view.layer.cornerRadius = 18
        view.isUserInteractionEnabled = true
        return view
    }()
    
    let commentImageView: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "video_comment_edit"))
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    let commentPlaceholderLabel: UILabel = {
        let label = UILabel()
        label.text = "说点什么吧..."
        label.textColor = UIColor.white.withAlphaComponent(0.6)
        label.font = .systemFont(ofSize: 14)
        label.isUserInteractionEnabled = false // 确保点击事件传递给父视图
        return label
    }()
    
    let userAvatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.backgroundColor = UIColor(hex: "#F5F5F5")
        imageView.layer.cornerRadius = 20
        imageView.clipsToBounds = true
        // 设置边框
        imageView.layer.borderWidth = 2
        imageView.layer.borderColor = UIColor(hex: "#FF5900").cgColor
        return imageView
    }()
    
    let userNameLabel: UILabel = {
        let label = UILabel()
        label.text = "作者名称"
        label.textColor = .white
        label.font = .systemFont(ofSize: 16, weight: .bold)
        return label
    }()
    
    let followButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("关注", for: .normal)
        button.setTitle("已关注", for: .selected)
        button.setTitleColor(.white, for: .normal)
        button.setTitleColor(UIColor(hex: "#AAAAAA"), for: .selected)
        button.titleLabel?.font = .systemFont(ofSize: 12)
        button.layer.cornerRadius = 12
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.clear.cgColor
        button.clipsToBounds = true
        return button
    }()
    
    let titleLabel: UILabel = {
        let label = UILabel()
        label.text = "标题标题标题标题标题标题标题"
        label.textColor = .white
        label.font = .systemFont(ofSize: 14)
        label.numberOfLines = 2
        return label
    }()
    
    let commentDropDownButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "comment_drop_down_arrow"), for: .normal)
        button.frame = CGRect(x: 0, y: 0, width: 31, height: 31)
        button.contentMode = .scaleAspectFit
        return button
    }()
    
    // 交互按钮容器视图
    lazy var interactionButtonContainers: [UIView] = {
        let configs = [
            ("video_like", "video_like_selected", "点赞"),
            ("video_collect", "video_collect_selected", "收藏"),
            ("video_share", nil, "分享"),
            ("video_comment", nil, "评论")
        ]

        return configs.enumerated().map { index, config in
            let containerView = UIView()
            containerView.backgroundColor = .clear
            containerView.tag = index

            // 创建图标
            let imageView = UIImageView()
            imageView.image = UIImage(named: config.0)
            imageView.contentMode = .scaleAspectFit
            imageView.tag = 100 // 用于后续查找
            containerView.addSubview(imageView)

            // 创建文字标签
            let label = UILabel()
            label.text = "99"
            label.textColor = .white
            label.font = .systemFont(ofSize: 12)
            label.textAlignment = .center
            label.tag = 200 // 用于后续查找
            containerView.addSubview(label)

            // 创建透明按钮
            let button = UIButton(type: .custom)
            button.backgroundColor = .clear
            button.tag = index
            containerView.addSubview(button)

            // 设置约束
            imageView.snp.makeConstraints { make in
                make.top.equalToSuperview()
                make.centerX.equalToSuperview()
                make.width.height.equalTo(24)
            }

            label.snp.makeConstraints { make in
                make.top.equalTo(imageView.snp.bottom).offset(2)
                make.centerX.equalToSuperview()
                make.bottom.equalToSuperview()
                make.left.right.equalToSuperview()
            }

            button.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }

            return containerView
        }
    }()

    // 保持原有的interactionButtons属性以兼容现有代码
    lazy var interactionButtons: [UIButton] = {
        return interactionButtonContainers.map { container in
            return container.subviews.first { $0 is UIButton } as! UIButton
        }
    }()
    
    let videoPlayerView: UIView = {
        let view = UIView()
        // 将随机背景色改为黑色
        view.backgroundColor = .black
        view.isUserInteractionEnabled = true
        return view
    }()
    
    let pauseButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "video_pause"), for: .normal)
        button.isHidden = true // 默认隐藏
        return button
    }()
    
    // MARK: - 初始化
    init() {}
    
    // MARK: - UI Setup
    func setupUI(in viewController: UIViewController,
                 commentTapAction: Selector,
                 videoPlayerTapAction: Selector,
                 followButtonTapAction: Selector,
                 interactionButtonTapAction: Selector,
                 commentDropDownTapAction: Selector) {
        
        // 设置拖动代理
        if let videoPage = viewController as? VideoPage {
            dragDelegate = videoPage
        }
        
        viewController.view.addSubview(mainContentView)
        mainContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 添加进度条
        mainContentView.addSubview(videoProgressView)
        videoProgressView.addSubview(progressBuffer)
        // 隐藏缓冲进度条，暂时不显示缓冲进度
        progressBuffer.isHidden = true
        videoProgressView.addSubview(progressForeground)
        mainContentView.addSubview(progressHandle) // 添加到mainContentView以便超出进度条范围
        
        // 添加视频播放器占位视图
        mainContentView.addSubview(videoPlayerView)
        videoPlayerView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(videoProgressView.snp.top)
        }
        
        // 将时间标签容器添加到视频播放器视图上层
        videoPlayerView.addSubview(timeLabelContainer)
        timeLabelContainer.addSubview(timeLabel)
        
        // 确保时间标签容器在视频上层
        videoPlayerView.bringSubviewToFront(timeLabelContainer)
        
        print("[ProgressBar] 时间标签容器已添加到视频播放器视图上层")
        
        // 获取是否需要TabBar偏移
        let needsTabBarOffset = (viewController as? VideoPage)?.needsTabBarOffset ?? true

        // 增加进度条的可点击区域
        videoProgressView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            // 根据是否需要TabBar偏移设置底部约束
            if needsTabBarOffset {
                make.bottom.equalTo(viewController.view.safeAreaLayoutGuide).offset(-58)
            } else {
                make.bottom.equalTo(viewController.view.safeAreaLayoutGuide)
            }
            make.height.equalTo(4) // 增加高度
        }
        
        // 设置缓冲层
        progressBuffer.snp.makeConstraints { make in
            make.leading.top.bottom.equalToSuperview()
            // 初始缓冲为0
        }
        // 创建缓冲层宽度约束（初始 0），后续只修改 constant 值
        bufferWidthConstraint = progressBuffer.widthAnchor.constraint(equalToConstant: 0)
        bufferWidthConstraint?.isActive = true
        
        // 设置进度条前景
        progressForeground.snp.makeConstraints { make in
            make.leading.top.bottom.equalToSuperview()
            // 宽度通过 progressWidthConstraint 常量控制
        }
        // 创建一个常量宽度约束（初始 0），后续只修改 constant 值，不再重复生成约束
        progressWidthConstraint = progressForeground.widthAnchor.constraint(equalToConstant: 0)
        progressWidthConstraint?.isActive = true
        
        // 设置拖动指示器（滑块）
        progressHandle.snp.makeConstraints { make in
            make.centerY.equalTo(videoProgressView)
            make.leading.equalTo(progressForeground.snp.trailing).offset(-8) // 中心点对齐进度条末端
            make.width.height.equalTo(12) // 默认尺寸
        }
        
        // 设置时间标签容器（显示在视频中央偏下位置）
        timeLabelContainer.snp.makeConstraints { make in
            make.centerX.equalTo(videoPlayerView) // 左右居中
            make.bottom.equalTo(videoPlayerView).offset(-100) // 距离底部100pt
            make.height.equalTo(44) // 增加高度
            make.width.greaterThanOrEqualTo(140) // 增加最小宽度
        }

        // 设置时间标签
        timeLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(10)
            make.bottom.equalToSuperview().offset(-10)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(24) // 增加高度
        }
        
        // 添加调试信息
        print("[ProgressBar] 时间标签容器约束设置完成")
        print("[ProgressBar] 时间标签容器父视图: \(timeLabelContainer.superview?.description ?? "nil")")
        print("[ProgressBar] 视频播放器视图: \(videoPlayerView.description)")
        
        // 添加拖动手势
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleProgressPan(_:)))
        videoProgressView.addGestureRecognizer(panGesture)
        videoProgressView.isUserInteractionEnabled = true
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleProgressTap(_:)))
        videoProgressView.addGestureRecognizer(tapGesture)
        
        // 增大进度条的可点击区域
        let progressTapArea = UIView()
        progressTapArea.backgroundColor = UIColor.clear
        progressTapArea.isUserInteractionEnabled = true
        mainContentView.addSubview(progressTapArea)
        
        // 设置更大的点击区域
        progressTapArea.snp.makeConstraints { make in
            make.left.right.equalTo(videoProgressView)
            make.centerY.equalTo(videoProgressView)
            make.height.equalTo(44) // 增大点击区域高度
        }
        
        // 为扩大的点击区域添加手势
        let expandedPanGesture = UIPanGestureRecognizer(target: self, action: #selector(handleProgressPan(_:)))
        progressTapArea.addGestureRecognizer(expandedPanGesture)
        
        let expandedTapGesture = UITapGestureRecognizer(target: self, action: #selector(handleProgressTap(_:)))
        progressTapArea.addGestureRecognizer(expandedTapGesture)
        
        // 添加点击手势到视频播放器
        let tap = UITapGestureRecognizer(target: viewController, action: videoPlayerTapAction)
        videoPlayerView.addGestureRecognizer(tap)
        
        // 添加暂停按钮到播放器视图中间
        videoPlayerView.addSubview(pauseButton)
        pauseButton.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(64)
        }
        
        // 添加底部黑色栏
        mainContentView.addSubview(videoBottomBar)
        videoBottomBar.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(videoProgressView.snp.bottom)
            make.bottom.equalTo(viewController.view)
        }
        
        // 添加评论输入框
        videoBottomBar.addSubview(commentInputView)
        commentInputView.addSubview(commentImageView)
        commentInputView.addSubview(commentPlaceholderLabel)
        
        // 添加点击手势
        let commentTapGesture = UITapGestureRecognizer(target: viewController, action: commentTapAction)
        commentInputView.addGestureRecognizer(commentTapGesture)
        
        commentInputView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.top.equalToSuperview().offset(16)
            make.width.equalTo(158)
            make.height.equalTo(38)
        }
        
        commentImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.size.equalTo(16)
            make.centerY.equalToSuperview()
        }
        
        commentPlaceholderLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(36)
            make.centerY.equalToSuperview()
        }
        
        // 添加所有互动按钮容器到视图层级
        for (index, container) in interactionButtonContainers.enumerated() {
            videoBottomBar.addSubview(container)
            // 为透明按钮添加点击事件
            if let button = container.subviews.first(where: { $0 is UIButton }) as? UIButton {
                button.addTarget(viewController, action: interactionButtonTapAction, for: .touchUpInside)
            }
        }

        // 设置互动按钮容器的约束 - 使用等宽分布
        let buttonWidth: CGFloat = 40  // 减小按钮宽度
        let leftMargin: CGFloat = 8    // 评论框右侧到第一个按钮的间距
        let rightMargin: CGFloat = 12  // 最后一个按钮到屏幕右侧的间距

        // 创建一个容器来包含所有按钮，实现等间距分布
        let buttonsStackView = UIStackView(arrangedSubviews: interactionButtonContainers)
        buttonsStackView.axis = .horizontal
        buttonsStackView.distribution = .equalSpacing
        buttonsStackView.alignment = .center
        videoBottomBar.addSubview(buttonsStackView)

        // 设置StackView约束
        buttonsStackView.snp.makeConstraints { make in
            make.left.equalTo(commentInputView.snp.right).offset(leftMargin)
            make.right.equalToSuperview().offset(-rightMargin)
            make.centerY.equalTo(commentInputView)
            make.height.equalTo(44)
        }

        // 设置每个按钮容器的宽度
        for container in interactionButtonContainers {
            container.snp.makeConstraints { make in
                make.width.equalTo(buttonWidth)
                make.height.equalTo(44)
            }
        }
        
        // 添加用户信息
        mainContentView.addSubview(userAvatarImageView)
        mainContentView.addSubview(userNameLabel)
        mainContentView.addSubview(followButton)
        mainContentView.addSubview(titleLabel)
        mainContentView.addSubview(commentDropDownButton)
        
        // 添加关注按钮点击事件
        followButton.addTarget(viewController, action: followButtonTapAction, for: .touchUpInside)
        
        // 添加评论下拉按钮点击事件
        commentDropDownButton.addTarget(viewController, action: commentDropDownTapAction, for: .touchUpInside)
        
        userAvatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.bottom.equalTo(titleLabel.snp.top).offset(-8)
            make.width.height.equalTo(40)
        }
        
        userNameLabel.snp.makeConstraints { make in
            make.left.equalTo(userAvatarImageView.snp.right).offset(8)
            make.centerY.equalTo(userAvatarImageView)
        }
        
        followButton.snp.makeConstraints { make in
            make.left.equalTo(userNameLabel.snp.right).offset(8)
            make.centerY.equalTo(userAvatarImageView)
            make.width.equalTo(56)
            make.height.equalTo(24)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(userAvatarImageView)
            make.right.equalTo(commentDropDownButton.snp.left).offset(-8)
            make.bottom.equalTo(videoProgressView.snp.top).offset(-12)
        }
        
        commentDropDownButton.snp.makeConstraints { make in
            make.centerY.equalTo(titleLabel)
            make.right.equalToSuperview().offset(-8)
            make.width.height.equalTo(31)
        }
    }
    
    // MARK: - 更新UI状态
    func updateFollowButtonState(isFollowed: Bool) {
        followButton.isSelected = isFollowed
        
        if isFollowed {
            // 已关注状态
            // 移除渐变背景层
            followButton.layer.sublayers?.forEach { layer in
                if layer is CAGradientLayer {
                    layer.removeFromSuperlayer()
                }
            }
            followButton.backgroundColor = .clear
            followButton.layer.borderColor = UIColor(hex: "#AAAAAA").cgColor
        } else {
            // 未关注状态
            followButton.backgroundColor = .clear // 先清除背景色
            followButton.layer.borderColor = UIColor.clear.cgColor
            // 添加渐变背景
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.followButton.addGradientBackground(colors: self.themeGradientColors)
            }
        }
    }
    
    func updateAvatarBorder() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.userAvatarImageView.addGradientBorder(width: 2, colors: self.themeGradientColors)
        }
    }
    
    func updateLikeButtonState(isLiked: Bool) {
        guard let container = interactionButtonContainers.first,
              let imageView = container.viewWithTag(100) as? UIImageView else { return }

        let imageName = isLiked ? "video_like_selected" : "video_like"
        imageView.image = UIImage(named: imageName)
    }

    func updateCollectButtonState(isCollected: Bool) {
        guard interactionButtonContainers.count > 1,
              let container = interactionButtonContainers[1] as UIView?,
              let imageView = container.viewWithTag(100) as? UIImageView else { return }

        let imageName = isCollected ? "video_collect_selected" : "video_collect"
        imageView.image = UIImage(named: imageName)
    }

    // 更新交互按钮的数字显示
    func updateInteractionButtonCount(buttonIndex: Int, count: Int) {
        guard buttonIndex < interactionButtonContainers.count else { return }

        let container = interactionButtonContainers[buttonIndex]
        guard let label = container.viewWithTag(200) as? UILabel else { return }

        let titles = ["点赞", "收藏", "分享", "评论"]

        if count == 0 {
            // 数据为0时显示标题
            label.text = titles[buttonIndex]
        } else {
            // 数据不为0时显示格式化的数字
            label.text = formatCount(count)
        }
    }

    // 格式化数字显示
    private func formatCount(_ count: Int) -> String {
        if count >= 10000 {
            let formattedCount = Double(count) / 10000.0
            return String(format: "%.1f万", formattedCount)
        } else {
            return "\(count)"
        }
    }
    
    func updatePauseButtonVisibility(isPlaying: Bool) {
        if !isPlaying {
            // 显示暂停按钮
            pauseButton.isHidden = false
            // 添加淡入动画
            pauseButton.alpha = 0
            UIView.animate(withDuration: 0.2) {
                self.pauseButton.alpha = 1
            }
            
            // 2秒后自动隐藏
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) { [weak self] in
                guard let self = self, !isPlaying else { return }
                UIView.animate(withDuration: 0.2) {
                    self.pauseButton.alpha = 0
                } completion: { _ in
                    self.pauseButton.isHidden = true
                }
            }
        } else {
            // 立即隐藏暂停按钮
            UIView.animate(withDuration: 0.2) {
                self.pauseButton.alpha = 0
            } completion: { [weak self] _ in
                self?.pauseButton.isHidden = true
            }
        }
    }
    
    // MARK: - 进度条交互
    @objc private func handleProgressPan(_ gesture: UIPanGestureRecognizer) {
        // 获取手势在进度条视图中的位置
        let location = gesture.location(in: videoProgressView)
        let progressWidth = videoProgressView.bounds.width
        
        // 计算进度百分比（0-1之间）
        var progress = Float(max(0, min(location.x / progressWidth, 1.0)))
        
        switch gesture.state {
        case .began:
            isDragging = true
            print("[ProgressBar] 开始拖动进度条，视频时长: \(videoDuration)")
            showDraggingUI(true)
            updateDraggingUI(progress: progress)
            dragDelegate?.progressDragBegan(progress: progress)
            
        case .changed:
            updateDraggingUI(progress: progress)
            dragDelegate?.progressDragChanged(progress: progress)
            
        case .ended, .cancelled:
            isDragging = false
            print("[ProgressBar] 结束拖动进度条")
            showDraggingUI(false)
            dragDelegate?.progressDragEnded(progress: progress)
            
        default:
            break
        }
    }
    
    @objc private func handleProgressTap(_ gesture: UITapGestureRecognizer) {
        // 获取手势在进度条视图中的位置
        let location = gesture.location(in: videoProgressView)
        let progressWidth = videoProgressView.bounds.width
        
        // 计算进度百分比（0-1之间）
        let progress = Float(max(0, min(location.x / progressWidth, 1.0)))
        
        // 显示点击反馈
        showTapFeedback(at: location)
        
        // 通知代理
        dragDelegate?.progressTapped(progress: progress)
    }
    
    private func showTapFeedback(at location: CGPoint) {
        // 创建一个临时视图作为点击反馈
        let feedbackView = UIView(frame: CGRect(x: 0, y: 0, width: 20, height: 20))
        feedbackView.backgroundColor = UIColor.white.withAlphaComponent(0.8)
        feedbackView.layer.cornerRadius = 10
        feedbackView.center = CGPoint(x: location.x, y: videoProgressView.bounds.height / 2)
        videoProgressView.addSubview(feedbackView)
        
        // 添加动画
        UIView.animate(withDuration: 0.3, animations: {
            feedbackView.transform = CGAffineTransform(scaleX: 1.5, y: 1.5)
            feedbackView.alpha = 0
        }) { _ in
            feedbackView.removeFromSuperview()
        }
    }
    
    // MARK: - 更新进度条
    func updateProgress(currentTime: Float, duration: Float, bufferProgress: Float = 0) {
        // 保存视频时长，用于拖动时显示时间
        self.videoDuration = duration

        // 添加调试信息
        print("[ProgressBar] 更新进度: 当前时间=\(currentTime), 总时长=\(duration), 缓冲进度=\(bufferProgress)")

        // 如果正在拖动，不更新进度
        if isDragging {
            print("[ProgressBar] 正在拖动中，跳过进度更新")
            return
        }

        // 计算进度值（0~1）
        let progressRatio: CGFloat = (duration > 0) ? CGFloat(min(max(currentTime / duration, 0), 1)) : 0

        // 调整进度条宽度
        progressWidthConstraint?.constant = videoProgressView.bounds.width * progressRatio

        // 只有当传入的缓冲进度大于0时才更新（避免覆盖事件驱动的缓冲进度）
        if bufferProgress > 0 {
            updateBufferProgress(bufferProgress)
        }

        // 强制立即更新布局
        videoProgressView.layoutIfNeeded()
        progressBuffer.layoutIfNeeded()
        progressForeground.layoutIfNeeded()

        // 使用动画更新布局
        UIView.animate(withDuration: 0.1) {
            self.videoProgressView.layoutIfNeeded()
        }
    }
    
    // 更新缓冲进度
    func updateBufferProgress(_ progress: Float) {
        // 确保进度值在有效范围内
        let clampedProgress = max(0.0, min(1.0, progress))

        // 获取进度条的实际宽度
        let progressViewWidth = videoProgressView.bounds.width

        // 如果宽度为0，说明布局还没完成，延迟执行
        if progressViewWidth <= 0 {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                self?.updateBufferProgress(progress)
            }
            return
        }

        // 计算缓冲层应该的宽度
        let bufferWidth = progressViewWidth * CGFloat(clampedProgress)

        // 添加调试信息
        print("[ProgressBar] 事件驱动更新缓冲进度: progress=\(progress), clampedProgress=\(clampedProgress), progressViewWidth=\(progressViewWidth), bufferWidth=\(bufferWidth)")

        // 更新缓冲层宽度约束
        bufferWidthConstraint?.constant = bufferWidth

        // 强制立即更新布局
        progressBuffer.setNeedsLayout()
        progressBuffer.layoutIfNeeded()

        // 使用动画更新布局
        UIView.animate(withDuration: 0.2) {
            self.videoProgressView.layoutIfNeeded()
        }
    }
    
    // 显示拖动时的UI
    private func showDraggingUI(_ show: Bool) {
        print("[ProgressBar] 显示拖动UI: \(show)")
        print("[ProgressBar] 拖动前 - 时间标签容器隐藏: \(timeLabelContainer.isHidden), 时间标签文本: \(timeLabel.text ?? "nil")")
        
        UIView.animate(withDuration: 0.2) {
            // 滑块始终可见，只是拖动时放大
            self.timeLabelContainer.alpha = show ? 1.0 : 0.0
            self.timeLabelContainer.isHidden = !show

            // 拖动时增加进度条高度
            self.videoProgressView.transform = show ?
            CGAffineTransform(scaleX: 1.0, y: 1.5) : .identity

            // 拖动时滑块效果
            if show {
                // 增大滑块尺寸
                self.progressHandle.transform = CGAffineTransform(scaleX: 1.3, y: 1.3)
                // 增加前景亮度和发光效果
                self.progressForeground.backgroundColor = UIColor.white
                self.progressForeground.layer.shadowColor = UIColor.white.cgColor
                self.progressForeground.layer.shadowOffset = CGSize.zero
                self.progressForeground.layer.shadowOpacity = 0.8
                self.progressForeground.layer.shadowRadius = 4
            } else {
                // 恢复滑块尺寸
                self.progressHandle.transform = .identity
                self.progressForeground.backgroundColor = UIColor.white.withAlphaComponent(0.6)
                self.progressForeground.layer.shadowOpacity = 0
            }
        } completion: { _ in
            print("[ProgressBar] 拖动UI动画完成 - 时间标签容器隐藏: \(self.timeLabelContainer.isHidden), 时间标签文本: \(self.timeLabel.text ?? "nil")")
        }
        
        // 添加调试信息
        print("[ProgressBar] 拖动UI显示状态: \(show), 时间标签容器隐藏: \(timeLabelContainer.isHidden)")
    }
    
    private func updateDraggingUI(progress: Float) {
        // 更新进度条
        progressWidthConstraint?.constant = videoProgressView.bounds.width * CGFloat(progress)

        // 更新时间标签
        if videoDuration > 0 {
            let currentTime = progress * videoDuration
            let currentTimeStr = formatTime(seconds: Int(currentTime))
            let totalTimeStr = formatTime(seconds: Int(videoDuration))
            let timeText = "\(currentTimeStr) / \(totalTimeStr)"
            timeLabel.text = timeText
            
            // 添加调试信息
            print("[ProgressBar] 更新时间标签: \(timeText), 视频时长: \(videoDuration), 进度: \(progress)")
            print("[ProgressBar] 时间标签容器隐藏状态: \(timeLabelContainer.isHidden), 时间标签文本: \(timeLabel.text ?? "nil")")
        } else {
            print("[ProgressBar] 视频时长无效: \(videoDuration)")
            // 如果视频时长无效，显示默认文本
            timeLabel.text = "00:00 / 00:00"
        }

        // 立即更新布局
        videoProgressView.layoutIfNeeded()
        timeLabelContainer.layoutIfNeeded()
        
        // 强制刷新时间标签容器
        timeLabelContainer.setNeedsLayout()
        timeLabelContainer.layoutIfNeeded()
    }

    private func formatTime(seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }

    // MARK: - 进度条显示控制
    /// 隐藏所有进度条相关UI（用于笔记类型作品）
    func hideProgressBarUI() {
        videoProgressView.isHidden = true
        progressHandle.isHidden = true
        timeLabelContainer.isHidden = true
        print("[ProgressBar] 已隐藏所有进度条相关UI（笔记类型作品）")
    }

    /// 显示所有进度条相关UI（用于视频类型作品）
    func showProgressBarUI() {
        videoProgressView.isHidden = false
        progressHandle.isHidden = false
        // 时间标签容器默认隐藏，只在拖动时显示
        timeLabelContainer.isHidden = true
        print("[ProgressBar] 已显示所有进度条相关UI（视频类型作品）")
    }
}

// MARK: - 进度条拖动代理协议
protocol VideoProgressDragDelegate: AnyObject {
    func progressDragBegan(progress: Float)
    func progressDragChanged(progress: Float)
    func progressDragEnded(progress: Float)
    func progressTapped(progress: Float)
} 
