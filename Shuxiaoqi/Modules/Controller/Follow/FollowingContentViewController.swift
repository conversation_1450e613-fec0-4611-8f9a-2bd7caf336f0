//
//  FollowingContentViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/3/24.
//

import UIKit
import MJRefresh

// 关注内容控制器
class FollowingContentViewController: BaseViewController, UITableViewDelegate, UITableViewDataSource {
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = UIColor(hex: "#F5F5F5") // 浅灰色背景
        tableView.register(FollowingCell.self, forCellReuseIdentifier: "FollowingCell")
        tableView.register(RecommendedFollowCell.self, forCellReuseIdentifier: "RecommendedFollowCell")
        tableView.register(UITableViewHeaderFooterView.self, forHeaderFooterViewReuseIdentifier: "SectionHeader")
        tableView.separatorStyle = .none
        tableView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 8, right: 0)
        tableView.sectionHeaderTopPadding = 0
        return tableView
    }()
    
    // 新增：分页变量
    private var currentPage: Int = 0
    private let pageSize: Int = 10
    private var isFetchingData: Bool = false
    private var allDataLoaded: Bool = false
    private var totalFollowing: Int = 0
    
    // 新增：空列表占位视图
    private lazy var emptyPlaceholderView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        view.isHidden = true
        
        let imageView = UIImageView()
        imageView.image = UIImage(named: "empty_data_placeholder_image")
        imageView.contentMode = .scaleAspectFit
        view.addSubview(imageView)
        
        let label = UILabel()
        label.text = "暂无关注"
        label.textColor = UIColor.gray
        label.font = UIFont.systemFont(ofSize: 16)
        label.textAlignment = .center
        view.addSubview(label)
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-60)
            make.width.height.equalTo(140)
        }
        
        label.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(20)
        }
        
        return view
    }()
    
    // 用户数据 - 将被实际API数据替换
    private var users: [[String: String]] = []
    
    // 新增：推荐关注数据
    private var recommendedUsers: [[String: String]] = []
    
    // 是否显示推荐关注
    private var showRecommended: Bool = true
    
    // Section 标识
    private enum Section: Int {
        case recommended = 0
        case following = 1
    }
    
    // 新增：搜索关键字属性
    private var searchKeyword: String = ""
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 明确设置背景色
        self.view.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 检查自定义视图设置
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        setupUI()
        
        // 新增：设置刷新控件
        setupRefreshControls()
        
        // 新增：设置空白占位视图
        setupPlaceholderView()
        
        // 首次加载数据
        tableView.mj_header?.beginRefreshing()
        
        // 重要：触发布局更新
        view.setNeedsLayout()
        view.layoutIfNeeded()
    }
    
    // 新增：设置刷新控件
    private func setupRefreshControls() {
        tableView.mj_header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(loadNewData))
        if let header = tableView.mj_header {
            header.ignoredScrollViewContentInsetTop = tableView.contentInset.top
        }
        
        let footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(loadMoreData))
        footer.setTitle("", for: .idle)
        footer.setTitle("正在加载更多...", for: .refreshing)
        footer.setTitle("没有更多关注了", for: .noMoreData)
        tableView.mj_footer = footer
    }
    
    // 新增：设置占位视图方法
    private func setupPlaceholderView() {
        view.addSubview(emptyPlaceholderView)
        emptyPlaceholderView.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.top.equalToSuperview().offset(50)
        }
    }
    
    // 新增：更新占位视图可见性
    private func updatePlaceholderViewVisibility() {
        // 如果在搜索模式下（有搜索关键字），只考虑搜索结果是否为空
        let shouldShowEmpty: Bool
        if !searchKeyword.isEmpty {
            // 搜索模式：只要搜索结果为空就显示空状态
            shouldShowEmpty = users.isEmpty
        } else {
            // 非搜索模式：需要用户列表和推荐用户都为空才显示空状态
            shouldShowEmpty = users.isEmpty && recommendedUsers.isEmpty
        }

        if shouldShowEmpty {
            tableView.isHidden = true
            emptyPlaceholderView.isHidden = false
            if tableView.mj_footer?.isRefreshing ?? false {
                tableView.mj_footer?.endRefreshing()
            }
            tableView.mj_footer?.endRefreshingWithNoMoreData()
        } else {
            tableView.isHidden = false
            emptyPlaceholderView.isHidden = true
        }
    }
    
    // 新增：加载新数据（下拉刷新）
    @objc private func loadNewData() {
        guard !isFetchingData else { return }
        currentPage = 0
        allDataLoaded = false
        tableView.mj_footer?.resetNoMoreData()

        // 如果在搜索模式下，只刷新搜索结果，不刷新推荐用户
        if !searchKeyword.isEmpty {
            fetchFollowingData(isRefresh: true)
        } else {
            // 非搜索模式：同时获取推荐关注和我的关注数据
            fetchRecommendedUsers()
            fetchFollowingData(isRefresh: true)
        }
    }
    
    // 新增：加载更多数据（上拉加载）
    @objc private func loadMoreData() {
        guard !isFetchingData, !allDataLoaded else {
            if allDataLoaded {
                tableView.mj_footer?.endRefreshingWithNoMoreData()
            }
            return
        }
        fetchFollowingData(isRefresh: false)
    }
    
    // 新增：获取关注数据
    private func fetchFollowingData(isRefresh: Bool) {
        isFetchingData = true
        
        let pageToFetch = isRefresh ? 0 : currentPage
        let name = isRefresh ? searchKeyword : searchKeyword
        APIManager.shared.getFollowList(page: pageToFetch, size: pageSize, name: name) { [weak self] result in
            guard let self = self else { return }
            self.isFetchingData = false
            
            // 如果是下拉刷新，需要判断推荐数据是否也加载完毕
            if isRefresh {
                // 暂时不结束刷新，等待推荐数据加载完成
            } else {
                // 上拉加载更多时直接结束刷新
                self.tableView.mj_footer?.endRefreshing()
            }
            
            switch result {
            case .success(let apiResponse):
                // 处理从API获取的数据
                let newFollowingListOptional = apiResponse.data?.list
                
                // 更新总关注数 - 只刷新表格不设置标签文本
                if let total = apiResponse.data?.total {
                    self.totalFollowing = total
                }
                
                // 将API数据映射为本地数据格式
                let mappedUsersOptional: [[String: String]]? = newFollowingListOptional?.map {
                    [
                        "id": $0.customerId,
                        "name": $0.displayNickName,
                        "avatar": $0.wxAvator,
                        "firstShowLine": $0.firstShowLine,
                        "secondShowLine": $0.secondShowLine,
                        "personDetailInfo": $0.personDetailInfo,
                        "followEachState": $0.followEachState ? "1" : "0" // 新增：互相关注状态
                    ]
                }
                
                // 安全解包并检查数据是否为空
                guard let actualNewUsers = mappedUsersOptional, !actualNewUsers.isEmpty else {
                    if isRefresh {
                        self.users = []
                    }
                    self.allDataLoaded = true
                    self.tableView.mj_footer?.endRefreshingWithNoMoreData()
                    
                    // 只有在下拉刷新且两个数据源都加载完成时才结束刷新
                    if isRefresh {
                        self.tableView.mj_header?.endRefreshing()
                    }
                    
                    self.updatePlaceholderViewVisibility()
                    return
                }
                
                // 到这里，actualNewUsers是非空的用户数组
                if isRefresh {
                    self.users = actualNewUsers
                } else {
                    self.users.append(contentsOf: actualNewUsers)
                }
                
                // 分页逻辑
                guard let actualPageData = apiResponse.data else {
                    self.allDataLoaded = true
                    self.tableView.mj_footer?.endRefreshingWithNoMoreData()
                    
                    // 只有在下拉刷新且两个数据源都加载完成时才结束刷新
                    if isRefresh {
                        self.tableView.mj_header?.endRefreshing()
                    }
                    
                    self.updatePlaceholderViewVisibility()
                    return
                }
                
                self.currentPage = pageToFetch + 1
                
                if actualNewUsers.count < self.pageSize || (self.users.count >= actualPageData.total && actualPageData.total > 0) {
                    self.allDataLoaded = true
                    self.tableView.mj_footer?.endRefreshingWithNoMoreData()
                } else {
                    self.allDataLoaded = false
                    self.tableView.mj_footer?.endRefreshing()
                }
                
                // 在下拉刷新时，如果推荐数据也加载完成，结束刷新
                if isRefresh {
                    self.tableView.mj_header?.endRefreshing()
                }
                
                self.tableView.reloadData()
                self.updatePlaceholderViewVisibility()
                
            case .failure(let error):
                print("获取关注列表失败: \(error.localizedDescription)")
                if isRefresh {
                    self.tableView.mj_header?.endRefreshing()
                } else {
                    self.tableView.mj_footer?.endRefreshing()
                }
            }
        }
    }
    
    // 新增：获取推荐关注数据
    private func fetchRecommendedUsers() {
        APIManager.shared.getAttentionList { [weak self] result in
            guard let self = self else { return }
            
            switch result {
            case .success(let apiResponse):
                // 处理API返回的推荐用户数据
                let recommendedList = apiResponse.data ?? []
                
                // 将API数据映射为本地数据格式
                self.recommendedUsers = recommendedList.map {
                    [
                        "id": $0.customerId,
                        "name": $0.nickName,
                        "avatar": $0.wxAvator,
                        "firstShowLine": $0.firstShowLine,
                        "secondShowLine": $0.secondShowLine,
                        "isFollowed": "0"  // 默认设置为未关注
                    ]
                }
                
                // 如果没有推荐用户，则不显示推荐section
                self.showRecommended = !self.recommendedUsers.isEmpty
                
                // 刷新表格
                self.tableView.reloadData()
                
            case .failure(let error):
                print("获取推荐关注失败: \(error.localizedDescription)")
                self.showRecommended = false
                self.recommendedUsers = []
                self.tableView.reloadData()
            }
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        // 确保背景色正确
        self.view.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 检查父视图背景色
        if let parentView = self.view.superview {
            parentView.backgroundColor = UIColor(hex: "#F5F5F5")
        }
        
        // 确保表格背景色正确
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
    }
    
    private func setupUI() {
        showNavBar = false
        
        // 设置视图的背景色
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 设置表格
        view.addSubview(tableView)
        
        // 设置表格视图约束，使其置顶
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - UITableViewDataSource
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return showRecommended ? 2 : 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if showRecommended && section == Section.recommended.rawValue {
            return 1 // 推荐关注部分只有一个cell，包含水平滚动的推荐用户
        } else {
            return users.count // 我的关注部分
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if showRecommended && indexPath.section == Section.recommended.rawValue {
            // 推荐关注部分
            let cell = tableView.dequeueReusableCell(withIdentifier: "RecommendedFollowCell", for: indexPath) as! RecommendedFollowCell
            cell.configure(with: recommendedUsers)
            
            // 设置关注回调
            cell.onFollowTapped = { [weak self] userId in
                self?.followRecommendedUser(userId: userId)
            }
            
            // 设置取消关注回调
            cell.onUnfollowTapped = { [weak self] userId in
                self?.unfollowRecommendedUser(userId: userId)
            }
            
            // 设置用户点击回调
            cell.onUserTapped = { [weak self] userId in
                self?.navigateToUserProfile(userId: userId)
            }
            
            return cell
        } else {
            // 我的关注部分
            let cell = tableView.dequeueReusableCell(withIdentifier: "FollowingCell", for: indexPath) as! FollowingCell
            cell.configure(with: users[indexPath.row])
            
            // 设置点击回调
            cell.onCellTapped = { [weak self] userId in
                self?.navigateToUserProfile(userId: userId)
            }
            
            // 设置状态按钮点击回调
            cell.onStatusButtonTapped = { [weak self] in
                self?.unfollowUser(at: indexPath)
            }
            
            return cell
        }
    }
    
    // MARK: - UITableViewDelegate
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if showRecommended && indexPath.section == Section.recommended.rawValue {
            return 165 // 推荐关注部分的高度
        } else {
            return 84 // 我的关注部分的高度
        }
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: "SectionHeader")
        
        // 移除可能存在的旧子视图
        headerView?.contentView.subviews.forEach { $0.removeFromSuperview() }
        let titleLabel = UILabel()
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        titleLabel.textColor = UIColor(hex: "#333333")
        
        if section == Section.recommended.rawValue {
            titleLabel.text = "推荐关注"
        } else {
            // 如果是关注列表section，显示关注数量
            titleLabel.text = "我的关注"
            
            // 添加关注数量标签
            let countLabel = UILabel()
            countLabel.font = UIFont.systemFont(ofSize: 12)
            countLabel.textColor = UIColor(hex: "#666666")
            countLabel.text = "共\(totalFollowing)人"
            countLabel.textAlignment = .right
            
            headerView?.contentView.addSubview(countLabel)
            countLabel.snp.makeConstraints { make in
                make.right.equalToSuperview().offset(-16)
                make.centerY.equalToSuperview()
            }
        }
        
        headerView?.contentView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }
        
        // 设置headerView的背景色
        headerView?.contentView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        return headerView
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 40
    }
    
    // 在FollowingContentViewController中添加视图布局完成后的方法
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 确保视图和表格背景色正确
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 检查并修复父视图背景色
        if let parentView = view.superview {
            if parentView.backgroundColor != UIColor(hex: "#F5F5F5") {
                parentView.backgroundColor = UIColor(hex: "#F5F5F5")
            }
        }
    }
    
    // 添加表格代理方法处理行选择
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        // 只处理我的关注部分的点击（推荐关注部分的点击由cell内部处理）
        if indexPath.section == Section.following.rawValue {
            let user = users[indexPath.row]
            guard let userId = user["id"] else { return }
            navigateToUserProfile(userId: userId)
        }
    }
    
    // 导航到用户个人主页
    private func navigateToUserProfile(userId: String) {
        if let parentVC = parent as? FollowListViewController {
            parentVC.navigateToPersonalHomepage(userId: userId)
        }
    }
    
    // 关注推荐用户
    private func followRecommendedUser(userId: String) {
        APIManager.shared.followUser‌(customerId: userId, type: 1, worksId: 0) { [weak self] result in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                switch result {
                case .success:
                    self.showToast("关注成功")
                    // 1. 更新推荐用户数据源和按钮状态
                    if let index = self.recommendedUsers.firstIndex(where: { $0["id"] == userId }) {
                        self.recommendedUsers[index]["isFollowed"] = "1"
                        if let cell = self.tableView.cellForRow(at: IndexPath(row: 0, section: Section.recommended.rawValue)) as? RecommendedFollowCell {
                            cell.updateFollowButtonForUser(userId: userId, isFollowed: true)
                        }
                    }
                    // 2. 刷新下方的个     人关注列表
                    self.fetchFollowingData(isRefresh: true)
                    // 可选：也可刷新推荐列表（如需同步按钮状态）
                    // self.fetchRecommendedUsers()
                case .failure(let error):
                    // 处理错误情况，如果返回已关注错误，仍然更新UI
                    if error.localizedDescription.contains("已关注") || error.localizedDescription.contains("无需重新关注") {
                        self.showToast("已关注该用户")
                        // 同步更新推荐cell按钮状态
                        if let index = self.recommendedUsers.firstIndex(where: { $0["id"] == userId }) {
                            self.recommendedUsers[index]["isFollowed"] = "1"
                            if let cell = self.tableView.cellForRow(at: IndexPath(row: 0, section: Section.recommended.rawValue)) as? RecommendedFollowCell {
                                cell.updateFollowButtonForUser(userId: userId, isFollowed: true)
                            }
                        }
                        // 可选：刷新关注列表，防止状态不同步
                        self.fetchFollowingData(isRefresh: true)
                    } else {
                        print("关注推荐用户失败: \(error.localizedDescription)")
                        self.showToast("关注失败，请稍后重试")
                    }
                }
            }
        }
    }
    
    // 取消关注推荐用户
    private func unfollowRecommendedUser(userId: String) {
        // 替换为自定义弹窗
        let alert = CommonAlertView(
            title: "不再关注作者？",
            message: "",
            leftButtonTitle: "取消",
            rightButtonTitle: "不再关注"
        )
        alert.onLeftButtonTap = {
            alert.dismiss()
        }
        alert.onRightButtonTap = { [weak self] in
            guard let self = self else { return }
            // 调用取消关注API
            APIManager.shared.followUser‌(customerId: userId, type: 2, worksId: 0) { [weak self] result in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    switch result {
                    case .success:
                        self.showToast("已取消关注")
                        // 1. 更新推荐用户数据源和按钮状态
                        if let index = self.recommendedUsers.firstIndex(where: { $0["id"] == userId }) {
                            self.recommendedUsers[index]["isFollowed"] = "0"
                            if let cell = self.tableView.cellForRow(at: IndexPath(row: 0, section: Section.recommended.rawValue)) as? RecommendedFollowCell {
                                cell.updateFollowButtonForUser(userId: userId, isFollowed: false)
                            }
                        }
                        // 2. 统一刷新关注列表，保证和服务器同步
                        self.fetchFollowingData(isRefresh: true)
                    case .failure(let error):
                        print("取消关注失败: \(error.localizedDescription)")
                        self.showToast("操作失败，请稍后重试")
                    }
                }
            }
            alert.dismiss()
        }
        // 使用更高层视图展示弹窗，确保覆盖导航栏和搜索视图
        let targetView = self.view.window ?? self.parent?.view ?? self.view
        alert.show(in: targetView)
    }
    
    // 取消关注用户
    private func unfollowUser(at indexPath: IndexPath) {
        guard indexPath.section == Section.following.rawValue, indexPath.row < users.count else { return }
        let user = users[indexPath.row]
        guard let userId = user["id"] else { return }
        // 替换为自定义弹窗
        let alert = CommonAlertView(
            title: "不再关注作者？",
            message: "",
            leftButtonTitle: "取消",
            rightButtonTitle: "不再关注"
        )
        alert.onLeftButtonTap = {
            alert.dismiss()
        }
        alert.onRightButtonTap = { [weak self] in
            guard let self = self else { return }
            // 调用取消关注API
            APIManager.shared.followUser‌(customerId: userId, type: 2, worksId: 0) { [weak self] result in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    switch result {
                    case .success:
                        self.showToast("已取消关注")
                        // 统一刷新关注列表，保证和服务器同步
                        self.fetchFollowingData(isRefresh: true)
                    case .failure(let error):
                        print("取消关注失败: \(error.localizedDescription)")
                        self.showToast("操作失败，请稍后重试")
                    }
                }
            }
            alert.dismiss()
        }
        // 使用更高层视图展示弹窗，确保覆盖导航栏和搜索视图
        let targetView = self.view.window ?? self.parent?.view ?? self.view
        alert.show(in: targetView)
    }
    
    // 新增：外部调用以更新搜索关键字并刷新列表
    func updateSearchKeyword(_ keyword: String) {
        self.searchKeyword = keyword
        self.currentPage = 0
        self.allDataLoaded = false
        self.tableView.mj_footer?.resetNoMoreData()

        // 如果有搜索关键字，隐藏推荐用户部分，只显示搜索结果
        if !keyword.isEmpty {
            self.showRecommended = false
        } else {
            // 如果搜索关键字为空，恢复显示推荐用户
            self.showRecommended = !self.recommendedUsers.isEmpty
        }

        self.fetchFollowingData(isRefresh: true)
    }
}

// MARK: - 推荐关注单元格
class RecommendedFollowCell: UITableViewCell, UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    // 推荐用户集合视图
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumInteritemSpacing = 12
        layout.minimumLineSpacing = 12
        layout.sectionInset = UIEdgeInsets(top: 10, left: 16, bottom: 10, right: 16)
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor(hex: "#F5F5F5")
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(RecommendedUserCell.self, forCellWithReuseIdentifier: "RecommendedUserCell")
        return collectionView
    }()
    
    // 推荐用户数据
    private var users: [[String: String]] = []
    
    // 回调
    var onFollowTapped: ((String) -> Void)?
    var onUnfollowTapped: ((String) -> Void)?
    var onUserTapped: ((String) -> Void)?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = UIColor(hex: "#F5F5F5")
        contentView.backgroundColor = UIColor(hex: "#F5F5F5")
        selectionStyle = .none
        
        contentView.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    func configure(with users: [[String: String]]) {
        self.users = users
        collectionView.reloadData()
    }
    
    // 更新特定用户的关注按钮状态
    func updateFollowButtonForUser(userId: String, isFollowed: Bool) {
        // 查找用户索引
        if let index = users.firstIndex(where: { $0["id"] == userId }) {
            // 找到对应的单元格
            let indexPath = IndexPath(item: index, section: 0)
            
            // 如果单元格可见，则更新
            if let cell = collectionView.cellForItem(at: indexPath) as? RecommendedUserCell {
                cell.updateFollowButtonState(isFollowed: isFollowed)
            }
        }
    }
    
    // MARK: - UICollectionViewDataSource
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return users.count
//        return 6
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "RecommendedUserCell", for: indexPath) as! RecommendedUserCell
        
        let user = users[indexPath.item]
        cell.configure(with: user)
        
        cell.onFollowTapped = { [weak self] in
            if let userId = user["id"] {
                self?.onFollowTapped?(userId)
            }
        }
        
        cell.onUnfollowTapped = { [weak self] in
            if let userId = user["id"] {
                self?.onUnfollowTapped?(userId)
            }
        }
        
        return cell
    }
    
    // MARK: - UICollectionViewDelegateFlowLayout
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 115, height: 165)
    }
    
    // MARK: - UICollectionViewDelegate
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if let userId = users[indexPath.item]["id"] {
            onUserTapped?(userId)
        }
    }
}
