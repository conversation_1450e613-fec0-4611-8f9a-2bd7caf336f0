//
//  FollowersContentViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yongsheng ye on 2025/3/24.
//

import UIKit
import JXSegmentedView
import MJRefresh

// 粉丝内容控制器
class FollowersContentViewController: BaseViewController, UITableViewDelegate, UITableViewDataSource {
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain) // 明确指定plain样式
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(FollowersCell.self, forCellReuseIdentifier: "FollowersCell")
        tableView.register(UITableViewHeaderFooterView.self, forHeaderFooterViewReuseIdentifier: "SectionHeader")
        tableView.separatorStyle = .none
        tableView.backgroundColor = UIColor(hex: "#F5F5F5") // 浅灰色背景
        tableView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 8, right: 0) // 移除顶部内边距
        
        // iOS 15及以上设置sectionHeaderTopPadding为0
        if #available(iOS 15.0, *) {
            tableView.sectionHeaderTopPadding = 0
        }
        
        return tableView
    }()
    
    // 新增：分页变量
    private var currentPage: Int = 0 // 当前页码，从0开始
    private let pageSize: Int = 10   // 每页数量
    private var isFetchingData: Bool = false // 是否正在请求数据
    private var allDataLoaded: Bool = false  // 是否已加载所有数据
    private var totalFollowers: Int = 0 // 总粉丝数
    
    // 新增：空列表占位视图
    private lazy var emptyPlaceholderView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F5F5F5") // 与背景色一致
        view.isHidden = true // 默认隐藏
        
        let imageView = UIImageView()
        imageView.image = UIImage(named: "empty_followers_placeholder") // 替换为您的占位图片名称
        imageView.contentMode = .scaleAspectFit
        view.addSubview(imageView)
        
        let label = UILabel()
        label.text = "暂无粉丝"
        label.textColor = UIColor.gray
        label.font = UIFont.systemFont(ofSize: 16)
        label.textAlignment = .center
        view.addSubview(label)
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-60) // 图片在文字上方一点
            make.width.height.equalTo(140) // 根据您的图片调整大小
        }
        
        label.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(20)
        }
        
        return view
    }()
    
    // 模拟用户数据 - 将被实际API数据替换
    private var users: [[String: String]] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 明确设置背景色 - 修改设置方式
        self.view.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 检查自定义视图设置
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 禁用自动内边距调整
        if #available(iOS 11.0, *) {
            tableView.contentInsetAdjustmentBehavior = .never
        } else {
            automaticallyAdjustsScrollViewInsets = false
        }
        
        setupUI()
        
        // 新增：设置刷新控件
        setupRefreshControls()
        
        // 新增：设置空白占位视图
        setupPlaceholderView()
        
        // 首次加载数据
        tableView.mj_header?.beginRefreshing()
        
        // 重要：触发布局更新
        view.setNeedsLayout()
        view.layoutIfNeeded()
    }
    
    // 新增：设置刷新控件
    private func setupRefreshControls() {
        tableView.mj_header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(loadNewData))
        // 告诉刷新头部忽略我们为tableView内容设置的顶部内边距
        if let header = tableView.mj_header {
            header.ignoredScrollViewContentInsetTop = tableView.contentInset.top
        }
        
        let footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(loadMoreData))
        footer.setTitle("", for: .idle)
        footer.setTitle("正在加载更多...", for: .refreshing)
        footer.setTitle("没有更多粉丝了", for: .noMoreData)
        tableView.mj_footer = footer
    }
    
    // 新增：设置占位视图方法
    private func setupPlaceholderView() {
        view.addSubview(emptyPlaceholderView)
        emptyPlaceholderView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // 新增：更新占位视图可见性
    private func updatePlaceholderViewVisibility() {
        if users.isEmpty {
            tableView.isHidden = true
            emptyPlaceholderView.isHidden = false
            if tableView.mj_footer?.isRefreshing ?? false {
                tableView.mj_footer?.endRefreshing()
            }
            tableView.mj_footer?.endRefreshingWithNoMoreData()
        } else {
            tableView.isHidden = false
            emptyPlaceholderView.isHidden = true
        }
    }
    
    // 新增：加载新数据（下拉刷新）
    @objc private func loadNewData() {
        guard !isFetchingData else { return }
        currentPage = 0
        allDataLoaded = false
        tableView.mj_footer?.resetNoMoreData() // 重置没有更多数据的状态
        fetchFollowersData(isRefresh: true)
    }
    
    // 新增：加载更多数据（上拉加载）
    @objc private func loadMoreData() {
        guard !isFetchingData, !allDataLoaded else {
            if allDataLoaded {
                tableView.mj_footer?.endRefreshingWithNoMoreData()
            }
            return
        }
        fetchFollowersData(isRefresh: false)
    }
    
    // 新增：获取粉丝数据
    private func fetchFollowersData(isRefresh: Bool) {
        isFetchingData = true
        
        let pageToFetch = isRefresh ? 0 : currentPage
        
        APIManager.shared.getFansList(page: pageToFetch, size: pageSize) { [weak self] result in
            guard let self = self else { return }
            self.isFetchingData = false
            self.tableView.mj_header?.endRefreshing()
            
            switch result {
            case .success(let apiResponse):
                // 处理从API获取的数据
                let newFansListOptional = apiResponse.data?.list
                
                // 更新总粉丝数
                if let total = apiResponse.data?.total {
                    self.totalFollowers = total
                    // 刷新表头以更新粉丝数
                    self.tableView.reloadData()
                }
                
                // 将API数据映射为本地数据格式
                let mappedUsers: [[String: String]] = newFansListOptional?.compactMap { fan in
                    // 对可能为nil的字段提供默认值（如空字符串）
                    return [
                        "id": fan.customerId,
                        "name": fan.displayNickName,
                        "avatar": fan.wxAvator,
                        "status": fan.follow ? "已互关" : "点击互关",
                        "description": fan.firstShowLine, // 处理可能为nil的personalitySign
                        "remark": fan.secondShowLine,
                        "personDetailInfo": fan.personDetailInfo
                    ]
                } ?? [] // 如果newFansListOptional是nil，则mappedUsers为[]
                
                // 安全解包并检查数据是否为空
                guard !mappedUsers.isEmpty else {
                    if isRefresh {
                        self.users = [] // 刷新时清空现有数据
                    }
                    self.allDataLoaded = true
                    self.tableView.mj_footer?.endRefreshingWithNoMoreData()
                    self.updatePlaceholderViewVisibility()
                    return
                }
                
                // 到这里，actualNewUsers是非空的用户数组
                if isRefresh {
                    self.users = mappedUsers // 直接赋值
                } else {
                    self.users.append(contentsOf: mappedUsers)
                }
                
                // 分页逻辑
                guard let actualPageData = apiResponse.data else {
                    self.allDataLoaded = true
                    self.tableView.mj_footer?.endRefreshingWithNoMoreData()
                    self.updatePlaceholderViewVisibility()
                    return
                }
                
                self.currentPage = pageToFetch + 1 // 准备下一页获取
                
                if mappedUsers.count < self.pageSize || (self.users.count >= actualPageData.total && actualPageData.total > 0) {
                    self.allDataLoaded = true
                    self.tableView.mj_footer?.endRefreshingWithNoMoreData()
                } else {
                    self.allDataLoaded = false
                    self.tableView.mj_footer?.endRefreshing()
                }
                
                self.tableView.reloadData()
                self.updatePlaceholderViewVisibility()
                
            case .failure(let error):
                print("获取粉丝列表失败: \(error.localizedDescription)")
                // 在非刷新加载更多时确保footer停止刷新
                if !isRefresh {
                    self.tableView.mj_footer?.endRefreshing()
                }
                // 可选：向用户显示错误消息
            }
        }
    }
    
    func followUser(at indexPath: IndexPath) {
        guard indexPath.row < users.count else { return }
        let user = users[indexPath.row]
        guard let userId = user["id"] else { return }
        // 根据当前关注状态决定操作
        if user["status"] == "已互关" {
            // 替换为自定义弹窗
            let alert = CommonAlertView(
                title: "不再关注作者？",
                message: "",
                leftButtonTitle: "取消",
                rightButtonTitle: "不再关注"
            )
            alert.onLeftButtonTap = {
                alert.dismiss()
            }
            alert.onRightButtonTap = { [weak self] in
                guard let self = self else { return }
                // 调用取消关注API
                APIManager.shared.followUser‌(customerId: userId, type: 2, worksId: 0) { [weak self] result in
                    guard let self = self else { return }
                    DispatchQueue.main.async {
                        switch result {
                        case .success:
                            // 更新本地数据状态
                            self.users[indexPath.row]["status"] = "点击互关"
                            // 刷新单元格
                            self.tableView.reloadRows(at: [indexPath], with: .none)
                            // 显示成功提示
                            self.showToast("已取消关注")
                        case .failure(let error):
                            print("取消关注失败: \(error.localizedDescription)")
                            self.showToast("操作失败，请稍后重试")
                        }
                    }
                }
                alert.dismiss()
            }
            // 使用更高层视图展示弹窗，确保覆盖导航栏和搜索视图
            let targetView = self.view.window ?? self.parent?.view ?? self.view
            alert.show(in: targetView)
        } else {
            // 直接调用关注API
            APIManager.shared.followUser‌(customerId: userId, type: 1, worksId: 0) { [weak self] result in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    switch result {
                    case .success:
                        // 更新本地数据状态
                        self.users[indexPath.row]["status"] = "已互关"
                        // 刷新单元格
                        self.tableView.reloadRows(at: [indexPath], with: .none)
                        // 显示成功提示
                        self.showToast("关注成功")
                    case .failure(let error):
                        print("关注失败: \(error.localizedDescription)")
                        self.showToast("关注失败，请稍后重试")
                    }
                }
            }
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        // 确保背景色正确
        self.view.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 检查父视图背景色
        if let parentView = self.view.superview {
            parentView.backgroundColor = UIColor(hex: "#F5F5F5")
        }
        
        // 确保表格背景色正确
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
    }
    
    private func setupUI() {
        // 设置视图的背景色
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 设置表格
        view.addSubview(tableView)
        
        // 设置表格视图约束，使其置顶
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - UITableViewDataSource
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return users.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "FollowersCell", for: indexPath) as! FollowersCell
        cell.configure(with: users[indexPath.row])
        
        // 添加关注回调
        cell.onStatusButtonTapped = { [weak self] in
            print("点击了关注按钮")
            //根据反馈状态调用关注按钮
            self?.followUser(at: indexPath)
        }
        
        // 添加清理粉丝回调
        cell.removeHandler = { [weak self] in
            self?.removeFollower(at: indexPath)
        }
        
        // 修改：设置主题橙色样式
        if users[indexPath.row]["status"] == "已互关" {
            cell.statusButton.setTitleColor(UIColor(hex: "#F57301"), for: .normal) // 设置文字颜色为主题橙色
            cell.statusButton.layer.borderColor = UIColor(hex: "#F57301").cgColor // 设置边框颜色为主题橙色
        } else {
            cell.statusButton.setTitleColor(UIColor(hex: "#FFFFFF"), for: .normal) // 默认文字颜色为白色
        }
        
        return cell
    }

    // 添加表头视图方法
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: "SectionHeader")
        
        // 移除可能存在的旧子视图
        headerView?.contentView.subviews.forEach { $0.removeFromSuperview() }
        
        // 创建一个自定义容器视图覆盖整个header区域
        let containerView = UIView()
        containerView.backgroundColor = UIColor(hex: "#F5F5F5")
        containerView.frame = CGRect(x: 0, y: 0, width: tableView.frame.width, height: 50)
        headerView?.contentView.addSubview(containerView)
        
        // 粉丝总数标签
        let titleLabel = UILabel()
        titleLabel.text = "粉丝总数"
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        
        // 粉丝数量标签
        let countLabel = UILabel()
        countLabel.text = "共\(totalFollowers)人"
        countLabel.textColor = UIColor(hex: "#333333")
        countLabel.font = UIFont.systemFont(ofSize: 12)
        countLabel.textAlignment = .right
        
        // 添加标签到容器视图
        containerView.addSubview(titleLabel)
        containerView.addSubview(countLabel)
        
        // 设置标签约束
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        countLabel.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            containerView.leftAnchor.constraint(equalTo: headerView!.contentView.leftAnchor),
            containerView.rightAnchor.constraint(equalTo: headerView!.contentView.rightAnchor),
            containerView.topAnchor.constraint(equalTo: headerView!.contentView.topAnchor),
            containerView.bottomAnchor.constraint(equalTo: headerView!.contentView.bottomAnchor),
            
            titleLabel.leftAnchor.constraint(equalTo: containerView.leftAnchor, constant: 16),
            titleLabel.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            
            countLabel.rightAnchor.constraint(equalTo: containerView.rightAnchor, constant: -16),
            countLabel.centerYAnchor.constraint(equalTo: containerView.centerYAnchor)
        ])
        
        return headerView
    }
    
    // 设置表头高度
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 50
    }
    
    // MARK: - UITableViewDelegate
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 84
    }
    
    // 在FollowersContentViewController中也添加类似方法
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 确保视图和表格背景色正确
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 检查并修复父视图背景色
        if let parentView = view.superview {
            if parentView.backgroundColor != UIColor(hex: "#F5F5F5") {
                parentView.backgroundColor = UIColor(hex: "#F5F5F5")
            }
        }
    }
    
    // 添加JXSegmentedListContainerViewListDelegate协议方法
    func listView() -> UIView {
        return view
    }
    
    // 添加表格代理方法处理行选择
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let user = users[indexPath.row]
        let userId = user["id"] ?? "\(indexPath.row)"
        
        // 获取父视图控制器并调用其导航方法
        if let parentVC = parent as? FollowListViewController {
            parentVC.navigateToPersonalHomepage(userId: userId)
        }
    }
    
    // 添加清理粉丝方法
    private func removeFollower(at indexPath: IndexPath) {
        // 替换为自定义弹窗
//        guard indexPath.row < users.count else { return }
//        let userToRemove = users[indexPath.row]
//        guard let userId = userToRemove["id"] else { return }
//        let alert = CommonAlertView(
//            title: "确认清理粉丝",
//            message: "确定要清理该粉丝吗？",
//            leftButtonTitle: "取消",
//            rightButtonTitle: "确定"
//        )
//        alert.onLeftButtonTap = {
//            alert.dismiss()
//        }
//        alert.onRightButtonTap = { [weak self] in
//            guard let self = self else { return }
//            // 调用API取消关注粉丝
//            APIManager.shared.unfollowUser(customerId: userId) { [weak self] result in
//                guard let self = self else { return }
//                DispatchQueue.main.async {
//                    switch result {
//                    case .success:
//                        self.showToast("清理成功")
//                        // 从数据源中移除用户
//                        self.users.remove(at: indexPath.row)
//                        // 从表格视图中移除单元格
//                        self.tableView.deleteRows(at: [indexPath], with: .fade)
//                        // 更新总粉丝数
//                        self.totalFollowers -= 1
//                        // 更新占位视图可见性
//                        self.updatePlaceholderViewVisibility()
//                    case .failure(let error):
//                        print("清理粉丝失败: \(error.localizedDescription)")
//                        // 向用户展示错误提示
//                        self.showToast("操作失败，请稍后重试")
//                    }
//                }
//            }
//            alert.dismiss()
//        }
//        alert.show(in: self.view)
    }
}
