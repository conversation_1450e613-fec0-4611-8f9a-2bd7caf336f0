//
//  ProfileCell.swift
//  <PERSON><PERSON><PERSON>qi
//
//  已废弃：个人中心顶部信息已由ProfileHeaderView替代
//  如需内容cell可重构此文件，否则可安全删除。
//
//  Created by yong<PERSON><PERSON> ye on 2025/4/30.
//

// ProfileCell已废弃，个人中心顶部信息请使用ProfileHeaderView作为tableHeaderView。

import UIKit
import SnapKit
import Kingfisher // 新增：导入 Kingfisher

// MARK: - ProfileCell
class ProfileCell: UITableViewCell {
    
    // MARK: - Properties
    
    // 头部背景图
    private let headerBackgroundView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill // 恢复 .scaleAspectFill 以填充宽度
        imageView.clipsToBounds = true // 裁剪超出部分（重要）
        imageView.backgroundColor = UIColor(hex: "F5F5F5")
        imageView.layer.anchorPoint = CGPoint(x: 0.5, y: 1)
        return imageView
    }()
    
    // 头部背景蒙版
    private let headerOverlayView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.isUserInteractionEnabled = false // Overlay shouldn't block interactions
        
        // 创建渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.white.withAlphaComponent(0.2).cgColor,
            UIColor(hex: "#F5F5F5").cgColor
        ]
        gradientLayer.locations = [0.0, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.0)
        gradientLayer.endPoint = CGPoint(x: 0.5, y: 1.0)
        
        view.layer.addSublayer(gradientLayer) // Add layer immediately
        return view
    }()
    
    // 头像容器 - 用于添加渐变边框
    private let avatarContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.clipsToBounds = false
        return view
    }()
    
    // 渐变边框层
    private let gradientBorderLayer: CAGradientLayer = {
        let layer = CAGradientLayer()
        layer.colors = [
            UIColor(hex: "#FF5900").cgColor,
            UIColor(hex: "#FF8D36").cgColor
        ]
        layer.startPoint = CGPoint(x: 0, y: 0)
        layer.endPoint = CGPoint(x: 1, y: 1)
        return layer
    }()
    
    // 头像图片视图
    private let avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor(hex: "#F5F5F5") // 背景色，以防图片加载失败
        imageView.image = UIImage(named: "default_avatar") // 默认头像
        return imageView
    }()
    
    // 用户名标签
    private let usernameLabel: UILabel = {
        let label = UILabel()
        label.text = "未登录" // 默认文本
        label.textColor = UIColor(hex: "#333333")
        label.font = .boldSystemFont(ofSize: 18)
        return label
    }()
    
    // UID标签
    private let uidLabel: UILabel = {
        let label = UILabel()
        label.text = "树小柒号： --" // 默认文本
        label.textColor = UIColor(hex: "#444444")
        label.font = .systemFont(ofSize: 11)
        return label
    }()
    
    //  IP属地
    private let locationLabel: UILabel = {
        let label = UILabel()
        label.text = "IP属地： --" // 默认文本
        label.textColor = UIColor(hex: "#444444")
        label.font = .systemFont(ofSize: 11)
        return label
    }()
    
    // 个性签名标签
    private let bioLabel: UILabel = {
        let label = UILabel()
        label.text = "编辑个性签名" // 默认文本
        label.textColor = UIColor(hex: "#777777")
        label.font = .systemFont(ofSize: 13)
        label.numberOfLines = 1
        return label
    }()
    
    // 编辑按钮
    private let editButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "edit_icon"), for: .normal)
        button.addTarget(self, action: #selector(editButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 会员卡片视图
    private let membershipCard: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 8
        view.clipsToBounds = true
        
        // 背景渐变层
        let bgLayer = CAGradientLayer()
        bgLayer.colors = [
            UIColor(hex: "#FFF9E3").cgColor,
            UIColor(hex: "#FFF0C9").cgColor
        ]
        bgLayer.locations = [0, 1]
        bgLayer.startPoint = CGPoint(x: 0, y: 0.5)
        bgLayer.endPoint = CGPoint(x: 1, y: 0.5)
        view.layer.addSublayer(bgLayer)
        
        return view
    }()
    
    // 会员标题
    private let membershipTitle: UILabel = {
        let label = UILabel()
        label.text = "F1普通会员"
        label.textColor = UIColor(hex: "#8D4600")
        label.font = .boldSystemFont(ofSize: 16)
        return label
    }()
    
    // 会员有效期
    private let membershipExpiry: UILabel = {
        let label = UILabel()
        label.text = "有效期至2025-2-25"
        label.textColor = UIColor(hex: "#C56F1A")
        label.font = .systemFont(ofSize: 12)
        return label
    }()
    
    // 会员权益标签
    private let benefitsLabel: UILabel = {
        let label = UILabel()
        label.text = "尊享12项专属权益"
        label.textColor = UIColor(hex: "#8D4600")
        label.font = .systemFont(ofSize: 12)
        return label
    }()
    
    // 会员权益箭头图标
    private let benefitsArrow: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "vip_arrow_right")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 新增：存储统计数据的 Count Label
    private let followingCountLabel = UILabel()
    private let followersCountLabel = UILabel()
    private let likesCountLabel = UILabel()
    
    // 点击回调
    var onFollowingTapped: (() -> Void)?
    var onFollowersTapped: (() -> Void)?
    var onAvatarTapped: (() -> Void)?
//    var onImageLoadComplete: (() -> Void)? // 恢复：图片加载完成回调
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func createStatsView(title: String, countLabel: UILabel) -> UIView {
        let container = UIView()
        
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 4
        stackView.alignment = .center
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.textColor = UIColor(hex: "#444444")
        titleLabel.font = .boldSystemFont(ofSize: 13)
        
        // 配置传入的 countLabel
        countLabel.text = "0" // 默认值
        countLabel.textColor = UIColor(hex: "#444444")
        countLabel.font = .boldSystemFont(ofSize: 13)
        
        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(countLabel) // 使用传入的 countLabel
        
        container.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(14)
        }
        
        container.isUserInteractionEnabled = true
        return container
    }
    
    private func setupStatsAndMembership() {
        
        // 使用实例属性创建视图，并存储 count label
        let followingStats = createStatsView(title: "关注", countLabel: followingCountLabel)
        let followersStats = createStatsView(title: "粉丝", countLabel: followersCountLabel)
        let likesStats = createStatsView(title: "获赞", countLabel: likesCountLabel)
        
        contentView.addSubview(followingStats)
        contentView.addSubview(followersStats)
        contentView.addSubview(likesStats)
        
        followingStats.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.top.equalTo(bioLabel.snp.bottom).offset(8) // 固定间距 8
        }
        followersStats.snp.makeConstraints { make in
            make.left.equalTo(followingStats.snp.right).offset(20) // 固定间距 20
            make.centerY.equalTo(followingStats)
        }
        likesStats.snp.makeConstraints { make in
            make.left.equalTo(followersStats.snp.right).offset(20) // 固定间距 20
            make.centerY.equalTo(followingStats)
        }
        
        let followingTapGesture = UITapGestureRecognizer(target: self, action: #selector(followingTapped))
        followingStats.addGestureRecognizer(followingTapGesture)
        let followersTapGesture = UITapGestureRecognizer(target: self, action: #selector(followersTapped))
        followersStats.addGestureRecognizer(followersTapGesture)
        
        contentView.addSubview(membershipCard)
        membershipCard.snp.makeConstraints { make in
            make.top.equalTo(followingStats.snp.bottom).offset(16 /*移除: + segmentWidth*/) // 固定间距 16
            make.left.equalTo(20)
            make.right.equalTo(-20)
            make.height.equalTo(64 /*移除: + segmentWidth*/) // 固定高度 64
        }
        
        membershipCard.addSubview(membershipTitle)
        membershipCard.addSubview(membershipExpiry)
        membershipCard.addSubview(benefitsLabel)
        membershipCard.addSubview(benefitsArrow)
        
        membershipTitle.snp.makeConstraints { make in
            make.left.equalTo(20)
            make.top.equalTo(16)
        }
        membershipExpiry.snp.makeConstraints { make in
            make.left.equalTo(membershipTitle.snp.left)
            make.top.equalTo(membershipTitle.snp.bottom).offset(4)
            make.height.equalTo(12)
        }
        benefitsArrow.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
            make.size.equalTo(32)
        }
        benefitsLabel.snp.makeConstraints { make in
            make.right.equalTo(benefitsArrow.snp.left)
            make.centerY.equalToSuperview()
        }
    }
    
    @objc private func followingTapped() {
        onFollowingTapped?()
    }
    
    @objc private func followersTapped() {
        onFollowersTapped?()
    }
    
    private func setupUI() {
        
        backgroundColor = UIColor(hex: "#F5F5F5")
        contentView.backgroundColor = .clear
        
        // --- 修改：先添加背景图，再确保它在最底层 ---
        contentView.addSubview(headerBackgroundView)
//        contentView.sendSubviewToBack(headerBackgroundView) // 确保背景在最下面
        
        headerBackgroundView.addSubview(headerOverlayView)
        
        // 使用计算出的固定高度 312 (根据新的顶部间距 24 计算得出)
        let initialHeight: CGFloat = 273// Define initial height based on calculation
        
        headerBackgroundView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(initialHeight)
            make.top.equalTo(contentView.snp.top).offset(120)
        }
        
        headerOverlayView.snp.makeConstraints { make in
            make.edges.equalToSuperview() // Overlay covers the background view
        }
        
        // 添加头像容器
        contentView.addSubview(avatarContainer)
        avatarContainer.layer.cornerRadius = 33 // (66 / 2)
        avatarContainer.snp.makeConstraints { make in
            make.top.equalTo(24 /*移除: + segmentWidth*/) // 固定顶部间距 110
            make.left.equalTo(20)
            make.size.equalTo(66 /*移除: + segments*/) // 固定尺寸 66x66
        }
        
        // 设置渐变边框
        // 使用固定尺寸计算 frame 和 cornerRadius
        gradientBorderLayer.frame = CGRect(x: -1, y: -1, width: 66 + 2, height: 66 + 2)
        gradientBorderLayer.cornerRadius = 34 // (66 + 2) / 2
        avatarContainer.layer.addSublayer(gradientBorderLayer)
        
        // 添加头像
        avatarContainer.addSubview(avatarImageView)
        avatarImageView.layer.cornerRadius = 32 // (64 / 2)
        avatarImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(64 /*移除: + segments*/) // 固定尺寸 64x64
        }
        
        // 为头像添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(avatarTapped))
        avatarContainer.isUserInteractionEnabled = true
        avatarContainer.addGestureRecognizer(tapGesture)
        
        // 添加用户名标签
        contentView.addSubview(usernameLabel)
        usernameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarContainer.snp.top).offset(6)
            make.left.equalTo(avatarContainer.snp.right).offset(8)
            make.height.equalTo(17) // 固定高度 17
        }
        
        // 添加UID标签
        contentView.addSubview(uidLabel)
        uidLabel.snp.makeConstraints { make in
            make.top.equalTo(usernameLabel.snp.bottom).offset(8)
            make.left.equalTo(usernameLabel.snp.left)
            make.height.equalTo(11) // 固定高度 12
        }
        
        contentView.addSubview(locationLabel)
        locationLabel.snp.makeConstraints { make in
            make.top.equalTo(uidLabel.snp.bottom).offset(5)
            make.left.equalTo(uidLabel.snp.left)
            make.height.equalTo(11) // 固定高度 12
        }
        
        // 添加个性签名标签
        contentView.addSubview(uidLabel)
        uidLabel.snp.makeConstraints { make in
            make.top.equalTo(uidLabel.snp.bottom).offset(8 /*移除: + segmentWidth*/) // 固定间距 4
            make.left.equalTo(uidLabel.snp.left)
            make.height.equalTo(11) // 固定高度 12
        }
        
        // 添加个性签名标签
        contentView.addSubview(bioLabel)
        bioLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarContainer.snp.bottom).offset(14 /*移除: + segmentWidth*/) // 固定间距 12
            make.left.equalTo(20)
            make.height.equalTo(19) // 固定高度 19
        }
        
        // 添加编辑按钮
        contentView.addSubview(editButton)
        editButton.snp.makeConstraints { make in
            make.size.equalTo(15)
            make.centerY.equalTo(bioLabel)
            make.right.equalTo(-20)
        }
        
        // 更新个性签名标签的右边距约束
        bioLabel.snp.makeConstraints { make in
            make.right.lessThanOrEqualTo(editButton.snp.left).offset(-8)
        }
        
        // Setup stats and membership card
        setupStatsAndMembership(/*移除: segmentWidth: segmentWidth*/) // 移除传递 segmentWidth
    }
    
    @objc private func avatarTapped() {
        onAvatarTapped?()
    }
    
    @objc private func editButtonTapped() {
        // Handle edit button tap
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        // --- 修改：禁用隐式动画更新蒙版渐变层 ---
        CATransaction.begin()
        CATransaction.setDisableActions(true)
        
        // Update other gradient frames if needed
        if let borderLayer = membershipCard.layer.sublayers?.first as? CAGradientLayer {
            borderLayer.frame = membershipCard.bounds
        }
        if let bgLayer = membershipCard.layer.sublayers?.last as? CAGradientLayer {
            bgLayer.frame = membershipCard.bounds.insetBy(dx: 1, dy: 1)
        }

        // Update header overlay's gradient layer frame whenever layout happens
        // This ensures it matches the overlay view's bounds correctly
        if let gradientLayer = headerOverlayView.layer.sublayers?.first as? CAGradientLayer {
           // Check if frame needs update
           if gradientLayer.frame != headerOverlayView.bounds {
               gradientLayer.frame = headerOverlayView.bounds
           }
        }
        
        CATransaction.commit()
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
//        // 重置UI元素为默认状态
//        configure(with: nil)
//        // 重置背景图变换
//        headerBackgroundView.transform = .identity
//        // 恢复：重置回调
//        onImageLoadComplete = nil
    }
    
    // Update background frame based on scroll offset
    func updateHeaderBackgroundFrame(offsetY: CGFloat, safeAreaTop: CGFloat, originalHeight: CGFloat) {

        let heightToUse = originalHeight
        let widthToUse = contentView.bounds.width
        guard widthToUse > 0, heightToUse > 0 else { return }

        // Get the gradient layer reference once
        let gradientLayer = headerOverlayView.layer.sublayers?.first as? CAGradientLayer

        // Use CATransaction to disable implicit animations for layer frame changes
        CATransaction.begin()
        CATransaction.setDisableActions(true)

        if offsetY < 0 {

            // 只应用缩放变换，锚点在底部中心会使其向上扩展
            let scale = 1.2 + abs(offsetY) / heightToUse
            headerBackgroundView.transform = CGAffineTransform(scaleX: scale, y: scale)

            // Update gradient layer frame immediately based on current overlay bounds
            gradientLayer?.frame = headerOverlayView.bounds

        } else {
            // Scrolling Up or Static
            // Reset transform first
            if headerBackgroundView.transform != .identity {
                 headerBackgroundView.transform = .identity
            }
            gradientLayer?.frame = headerOverlayView.bounds
        }
        
        CATransaction.commit()
    }

    // 新增：配置 Cell 的方法
    public func configure(with userInfo: UserInfoData?) {
        if let user = userInfo {
            // --- 更新头像 ---
            let placeholder = UIImage(named: "default_avatar")
            var avatarSet = false // 标记头像是否已设置
            if !user.wxAvator.isEmpty, let url = URL(string: user.wxAvator) {
                avatarImageView.kf.setImage(with: url, placeholder: placeholder) { [weak self] result in
//                    if case .success(_) = result {
//                        avatarSet = true
//                        // 成功加载后，确保布局更新
//                        self?.setNeedsLayout()
//                        self?.layoutIfNeeded()
//                        // 修改：在布局完成后触发回调
//                        self?.onImageLoadComplete?()
//                    }
                }
                // --- 更新背景图 ---
                // 只有在头像URL有效时才尝试加载背景图
                 headerBackgroundView.kf.setImage(with: url, placeholder: nil /* 背景可以没有占位图 */) { [weak self] result in
                     // 不论背景加载成功与否，头像设置后都应触发布局
                     // 但如果头像加载失败/URL无效，这里也需要确保布局更新
                     // if !avatarSet { // 如果头像没设置成功，也触发一次布局以使用默认图
//                          self?.setNeedsLayout()
//                          self?.layoutIfNeeded()
//                          // 修改：在布局完成后触发回调
//                          self?.onImageLoadComplete?()
                     // }
                 }
            } else {
                // 如果 URL 无效或为空
                avatarImageView.image = placeholder
                 headerBackgroundView.image = nil // 清除背景或设为默认
                 // 确保使用默认图时也更新布局
//                 setNeedsLayout()
//                 layoutIfNeeded()
//                 // 修改：在布局完成后触发回调
//                 onImageLoadComplete?()
             }
            setNeedsLayout()
            layoutIfNeeded()
            // --- 更新文本标签 ---
            usernameLabel.text = user.displayNickName
            uidLabel.text = "树小柒号：\(user.customerAccount.isEmpty ? "--" : user.customerAccount)"
            // 解析新旧结构个性签名
            let rawSnapshot = user.personalitySign?.snapshot ?? ""
            let parsedSign = parseSignature(snapshot: rawSnapshot, mapping: user.personalitySign?.mentionedUser)
            bioLabel.text = parsedSign.isEmpty ? "编辑个性签名" : parsedSign

            // 更新统计数据
            followingCountLabel.text = "\(user.followNumber)"
            followersCountLabel.text = "\(user.fansNumber)"
            likesCountLabel.text = "\(user.likeNumber)"

            // TODO: 根据用户信息更新会员卡片信息 (如果需要)
            // membershipTitle.text = ...
            // membershipExpiry.text = ...

        } else {
            // 设置为默认未登录状态
            avatarImageView.image = UIImage(named: "default_avatar")
            usernameLabel.text = "点击登录" // 或者 "未登录"
            uidLabel.text = "树小柒号：--"
            bioLabel.text = "登录后查看更多信息" // 或者 "编辑个性签名"
            followingCountLabel.text = "0"
            followersCountLabel.text = "0"
            likesCountLabel.text = "0"

            // 重置会员卡片为默认状态 (如果需要)
             membershipTitle.text = "开通会员"
             membershipExpiry.text = "尊享专属特权"
             // 确保未登录状态也更新布局
//             setNeedsLayout()
//             layoutIfNeeded()
             // 修改：在布局完成后触发回调
//             onImageLoadComplete?()
         }
    }

    // MARK: - Signature Helper (与 ProfileHeaderView 保持一致)
    private func parseSignature(snapshot: String, mapping: [String: String]?) -> String {
        var result = snapshot.replacingOccurrences(of: "\\", with: "")
        if let map = mapping {
            for (id, name) in map {
                result = result.replacingOccurrences(of: "@\(id)", with: "@\(name)")
            }
        }
        return result
    }
}
