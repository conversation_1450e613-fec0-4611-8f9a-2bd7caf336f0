// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 70;
	objects = {

/* Begin PBXBuildFile section */
		04ABF0F92D89040100E85541 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04ABF0F82D89040100E85541 /* AppDelegate.swift */; };
		04ABF0FB2D89040100E85541 /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04ABF0FA2D89040100E85541 /* SceneDelegate.swift */; };
		04ABF1022D89040400E85541 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 04ABF1012D89040400E85541 /* Assets.xcassets */; };
		04ABF1102D89040400E85541 /* ShuxiaoqiTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04ABF10F2D89040400E85541 /* ShuxiaoqiTests.swift */; };
		04ABF11A2D89040400E85541 /* ShuxiaoqiUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04ABF1192D89040400E85541 /* ShuxiaoqiUITests.swift */; };
		04ABF11C2D89040400E85541 /* ShuxiaoqiUITestsLaunchTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04ABF11B2D89040400E85541 /* ShuxiaoqiUITestsLaunchTests.swift */; };
		************************ /* NetworkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD0048302D963637001BD612 /* NetworkManager.swift */; };
		BD0048362D963637001BD612 /* APIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD00482F2D963637001BD612 /* APIService.swift */; };
		BD0048372D963637001BD612 /* APIResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD00482E2D963637001BD612 /* APIResponse.swift */; };
		BD00483C2D96D02D001BD612 /* TXLiteAVSDK_UGC.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD00483A2D96D02D001BD612 /* TXLiteAVSDK_UGC.xcframework */; };
		BD00483D2D96D02D001BD612 /* TXFFmpeg.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD0048392D96D02D001BD612 /* TXFFmpeg.xcframework */; };
		BD00483E2D96D02D001BD612 /* TXSoundTouch.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD00483B2D96D02D001BD612 /* TXSoundTouch.xcframework */; };
		BD0048402D96D174001BD612 /* Accelerate.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD00483F2D96D174001BD612 /* Accelerate.framework */; };
		BD0048422D96D17D001BD612 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD0048412D96D17D001BD612 /* SystemConfiguration.framework */; };
		BD0048442D96D188001BD612 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = BD0048432D96D188001BD612 /* libc++.tbd */; };
		BD0048462D96D192001BD612 /* libsqlite3.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = BD0048452D96D192001BD612 /* libsqlite3.tbd */; };
		BD0048482D96D19D001BD612 /* MetalKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD0048472D96D19D001BD612 /* MetalKit.framework */; };
		BD00484A2D96D1A6001BD612 /* VideoToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD0048492D96D1A6001BD612 /* VideoToolbox.framework */; };
		BD00484C2D96D1AB001BD612 /* ReplayKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD00484B2D96D1AB001BD612 /* ReplayKit.framework */; };
		BD00484E2D96D1B0001BD612 /* GLKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD00484D2D96D1B0001BD612 /* GLKit.framework */; };
		BD0048502D96D1B6001BD612 /* OpenAL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD00484F2D96D1B6001BD612 /* OpenAL.framework */; };
		BD0048522D96D1BD001BD612 /* CoreServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD0048512D96D1BD001BD612 /* CoreServices.framework */; };
		BD005AFC2DF2D29700F2A1AF /* ProfileHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD005AFB2DF2D29700F2A1AF /* ProfileHeaderView.swift */; };
		************************ /* VideoUploadParameterManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD02D4572DFD096000D5D2C9 /* VideoUploadParameterManager.swift */; };
		BD05791A2DEED84C008C14A2 /* InteractiveInformationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD0579192DEED84C008C14A2 /* InteractiveInformationViewController.swift */; };
		************************ /* CommissionChangeDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD079B0F2DE5898100FB2B57 /* CommissionChangeDetailViewController.swift */; };
		************************ /* CoinTransactionRecordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD079B112DE5A9B500FB2B57 /* CoinTransactionRecordViewController.swift */; };
		BD07B77F2D97E8F9003F8A24 /* VideoEditViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* VideoEditViewController.swift */; };
		************************ /* DeviceUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD08B83A2E33331300FE74E5 /* DeviceUtils.swift */; };
		BD0BCA4B2DF281F900F47F51 /* NotificationPopSelector.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD0BCA4A2DF281F900F47F51 /* NotificationPopSelector.swift */; };
		BD0BCA4D2DF2870300F47F51 /* VisiblePopSelector.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD0BCA4C2DF2870300F47F51 /* VisiblePopSelector.swift */; };
		BD0BCA4F2DF2913C00F47F51 /* FollowMessageListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD0BCA4E2DF2913C00F47F51 /* FollowMessageListViewController.swift */; };
		BD0BCA522DF2943A00F47F51 /* FollowMessageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD0BCA502DF2943A00F47F51 /* FollowMessageCell.swift */; };
		BD0BCA532DF2943A00F47F51 /* FollowMessageListViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD0BCA512DF2943A00F47F51 /* FollowMessageListViewModel.swift */; };
		************************ /* VolumeKeyCaptureManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD10F3912DF6DCAD00A7E8A4 /* VolumeKeyCaptureManager.swift */; };
		************************ /* ModeCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD10F3952DF7097B00A7E8A4 /* ModeCell.swift */; };
		BD133C072D9E75C500384522 /* UIViewController+Toast.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD133C062D9E75C500384522 /* UIViewController+Toast.swift */; };
		BD133C092D9E804F00384522 /* APIRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD133C082D9E804F00384522 /* APIRequest.swift */; };
		************************ /* APIRouter.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD133C0A2D9E805700384522 /* APIRouter.swift */; };
		BD133C0D2D9E806900384522 /* APIManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD133C0C2D9E806900384522 /* APIManager.swift */; };
		BD136CC22E17AC0200CEED05 /* VideoDisplayCenterUIComponents.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD136CBF2E17AC0200CEED05 /* VideoDisplayCenterUIComponents.swift */; };
		BD136CC32E17AC0200CEED05 /* VideoDisplayCenterViewProtocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD136CC02E17AC0200CEED05 /* VideoDisplayCenterViewProtocols.swift */; };
		BD136CC42E17AC0200CEED05 /* VideoPageUIComponents.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD136CC12E17AC0200CEED05 /* VideoPageUIComponents.swift */; };
		BD136CC52E17AC0200CEED05 /* UIViewExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD136CBE2E17AC0200CEED05 /* UIViewExtensions.swift */; };
		BD1EFCAE2E0AB5710079E39B /* NoteEditingDetailsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD1EFCAC2E0AB5710079E39B /* NoteEditingDetailsViewController.swift */; };
		BD1FEC242DD7668E00FC0AC8 /* IconMenuCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD1FEC212DD7668E00FC0AC8 /* IconMenuCell.swift */; };
		BD1FEC262DD767E200FC0AC8 /* SectionHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD1FEC252DD767E200FC0AC8 /* SectionHeaderView.swift */; };
		BD1FEC282DD7681800FC0AC8 /* RecommendListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD1FEC272DD7681800FC0AC8 /* RecommendListViewController.swift */; };
		BD1FEC2A2DD7687200FC0AC8 /* NormalListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD1FEC292DD7687200FC0AC8 /* NormalListViewController.swift */; };
		BD1FEC2C2DD768D100FC0AC8 /* DiscoverSearchResultCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD1FEC2B2DD768D100FC0AC8 /* DiscoverSearchResultCell.swift */; };
		BD1FEC2E2DD768FF00FC0AC8 /* CategorySelectionPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD1FEC2D2DD768FF00FC0AC8 /* CategorySelectionPopupView.swift */; };
		BD1FEC322DD769A700FC0AC8 /* CategoryPopupCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD1FEC312DD769A700FC0AC8 /* CategoryPopupCell.swift */; };
		BD212AA72DD426A7004329BB /* WebViewJavascriptBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = BD212A9F2DD426A7004329BB /* WebViewJavascriptBridge.m */; };
		BD212AA82DD426A7004329BB /* WebViewJavascriptBridgeBase.m in Sources */ = {isa = PBXBuildFile; fileRef = BD212AA32DD426A7004329BB /* WebViewJavascriptBridgeBase.m */; };
		BD212AA92DD426A7004329BB /* WebViewJavascriptBridge_JS.m in Sources */ = {isa = PBXBuildFile; fileRef = BD212AA12DD426A7004329BB /* WebViewJavascriptBridge_JS.m */; };
		BD212AAA2DD426A7004329BB /* WKWebViewJavascriptBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = BD212AA52DD426A7004329BB /* WKWebViewJavascriptBridge.m */; };
		BD212AAF2DD47756004329BB /* MusicListTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD212AAE2DD47756004329BB /* MusicListTableViewCell.swift */; };
		BD212AB12DD49C10004329BB /* MusicPanelView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD212AB02DD49C10004329BB /* MusicPanelView.swift */; };
		BD3710DD2E25105100267160 /* ExpandRepliesCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* ExpandRepliesCell.swift */; };
		BD3B8FBA2D8E4DFE005539BB /* RealNameAuthenticationResultViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD3B8FB92D8E4DFE005539BB /* RealNameAuthenticationResultViewController.swift */; };
		BD3B8FBC2D8EADC6005539BB /* FollowListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD3B8FBB2D8EADC6005539BB /* FollowListViewController.swift */; };
		BD3CDAF02E0C05D70017EEA3 /* BrowsingHistoryViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD3CDAEF2E0C05D70017EEA3 /* BrowsingHistoryViewController.swift */; };
		BD431E062DB3366500D9765F /* UserSharingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD431E052DB3366500D9765F /* UserSharingViewController.swift */; };
		BD43297C2DAD2ABD00188D9B /* GuideViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4329792DAD2ABD00188D9B /* GuideViewController.swift */; };
		************************ /* MentionParser.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD44476D2E2255B3008BB946 /* MentionParser.swift */; };
		BD4447722E22671E008BB946 /* AuthCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4447702E22671E008BB946 /* AuthCoordinator.swift */; };
		************************ /* RecordSettingsPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4828942DB874230068DBCC /* RecordSettingsPopupView.swift */; };
		************************ /* RecordGridView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4828962DB8758E0068DBCC /* RecordGridView.swift */; };
		BD49D2CF2DB2375100652DAE /* QRScanViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* QRScanViewController.swift */; };
		BD4A45BC2DDC21A5000C64F9 /* occupations.json in Resources */ = {isa = PBXBuildFile; fileRef = BD4A45BB2DDC21A5000C64F9 /* occupations.json */; };
		BD4A45BE2DDC5954000C64F9 /* university.json in Resources */ = {isa = PBXBuildFile; fileRef = BD4A45BD2DDC5954000C64F9 /* university.json */; };
		BD4A45CD2DDC8553000C64F9 /* UserInfoEditAddressViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4A45CC2DDC8553000C64F9 /* UserInfoEditAddressViewController.swift */; };
		BD4A45CF2DDC8636000C64F9 /* UserInfoEditSchoolViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4A45CE2DDC8636000C64F9 /* UserInfoEditSchoolViewController.swift */; };
		BD4A45D12DDC8F5E000C64F9 /* UserInfoEditSchoolList.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4A45D02DDC8F5E000C64F9 /* UserInfoEditSchoolList.swift */; };
		BD4BA84D2E00FF03008D19C7 /* ImageCropPreviewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4BA84B2E00FF03008D19C7 /* ImageCropPreviewController.swift */; };
		BD4CE3D22DE04EFB004287BE /* GoodsCheckstandViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4CE3D12DE04EFB004287BE /* GoodsCheckstandViewController.swift */; };
		BD4CE3D52DE065B3004287BE /* AddedFollowershipNotificationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4CE3D42DE065B3004287BE /* AddedFollowershipNotificationViewController.swift */; };
		BD4CE3D72DE09406004287BE /* LoginViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4CE3D62DE09406004287BE /* LoginViewModel.swift */; };
		BD4CE3D92DE09BA8004287BE /* UILabel+TapExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4CE3D82DE09BA8004287BE /* UILabel+TapExtension.swift */; };
		BD4F03552E03F0EE00326B5D /* UserInfoEditAddressCityListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4F03542E03F0EE00326B5D /* UserInfoEditAddressCityListViewController.swift */; };
		BD53A5102D8958B00064BA5F /* MeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD53A50F2D8958B00064BA5F /* MeViewController.swift */; };
		************************ /* MessageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD53A5112D8958C10064BA5F /* MessageViewController.swift */; };
		BD54967C2D92982500F2F6C5 /* CommentHistoryViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD54967B2D92982500F2F6C5 /* CommentHistoryViewController.swift */; };
		BD54968A2D92B6A400F2F6C5 /* CommentHistoryCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD5496892D92B6A400F2F6C5 /* CommentHistoryCell.swift */; };
		BD54968C2D92B6AA00F2F6C5 /* ReplyCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD54968B2D92B6AA00F2F6C5 /* ReplyCell.swift */; };
		BD57CE782D9A9D8800052EE1 /* VideoSDKTestViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD57CE772D9A9D8800052EE1 /* VideoSDKTestViewController.swift */; };
		BD5969EB2D895338001194A9 /* HomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* HomeViewController.swift */; };
		BD5AC9EF2DBC7D5F00350A6B /* ProductItemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD5AC9EE2DBC7D5F00350A6B /* ProductItemCell.swift */; };
		BD5AC9F22DBC7E2500350A6B /* DiscoverProductCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD5AC9F12DBC7E2500350A6B /* DiscoverProductCell.swift */; };
		BD5AC9F42DBC7E6200350A6B /* VideoCollectionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD5AC9F32DBC7E6200350A6B /* VideoCollectionCell.swift */; };
		BD5AC9F82DBCD39100350A6B /* RASManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD5AC9F72DBCD39100350A6B /* RASManager.swift */; };
		BD6560BF2E1CEE6D00084C6C /* NoteCarouselView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD6560BE2E1CEE6D00084C6C /* NoteCarouselView.swift */; };
		BD666FD72DC1BF8B0081F608 /* ProfileCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD666FD62DC1BF8B0081F608 /* ProfileCell.swift */; };
		BD666FD92DC1BFCA0081F608 /* FunctionsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD666FD82DC1BFCA0081F608 /* FunctionsCell.swift */; };
		BD666FDB2DC1BFE00081F608 /* ContentCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD666FDA2DC1BFE00081F608 /* ContentCell.swift */; };
		BD666FDD2DC1C0080081F608 /* ContentItemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD666FDC2DC1C0080081F608 /* ContentItemCell.swift */; };
		************************ /* ShareEvent.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD66ED642E0FEAE000E2C690 /* ShareEvent.swift */; };
		BD68D4F62D9CCCEC00C3E9BF /* VideoItemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* VideoItemCell.swift */; };
		BD695C4A2DF04CA8004A2F0F /* CommonAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD695C492DF04CA8004A2F0F /* CommonAlertView.swift */; };
		BD718A492E0D377300C781A6 /* APIManager+Search.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD718A482E0D377300C781A6 /* APIManager+Search.swift */; };
		BD7223DC2D8A5D5700496130 /* UIColor+Hex.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD7223DA2D8A5D5700496130 /* UIColor+Hex.swift */; };
		BD7223DE2D8A602100496130 /* UIView+Gradient.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD7223DD2D8A602100496130 /* UIView+Gradient.swift */; };
		BD7223E02D8A62D500496130 /* CustomTabBarController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD7223DF2D8A62D500496130 /* CustomTabBarController.swift */; };
		BD7223E22D8A66DD00496130 /* FriendViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD7223E12D8A66DD00496130 /* FriendViewController.swift */; };
		************************ /* SetNewPasswordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* SetNewPasswordViewController.swift */; };
		BD75A0902DAFB3BA006AEF62 /* UpgradePopupViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* UpgradePopupViewController.swift */; };
		BD75A0922DAFBBB0006AEF62 /* PreferenceTagsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* PreferenceTagsViewController.swift */; };
		BD7D42A02DE3FDE7007986FE /* CreativeVideoItemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD7D429F2DE3FDE7007986FE /* CreativeVideoItemCell.swift */; };
		BD7D42A22DE4876A007986FE /* LikedWorksViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD7D42A12DE4876A007986FE /* LikedWorksViewController.swift */; };
		BD83D2012D8A78A1002C65BC /* DiscoverViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* DiscoverViewController.swift */; };
		************************ /* StoreViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* StoreViewController.swift */; };
		BD83D2052D8A949B002C65BC /* AttentionViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* AttentionViewController.swift */; };
		BD83D2072D8AC670002C65BC /* LeftMenuViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* LeftMenuViewController.swift */; };
		BD83D20A2D8AEAF4002C65BC /* SettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* SettingViewController.swift */; };
		BD8BC84A2D9101DC00055118 /* FollowingContentViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD8BC8492D9101DC00055118 /* FollowingContentViewController.swift */; };
		BD8BC84C2D9101E400055118 /* FollowersContentViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD8BC84B2D9101E400055118 /* FollowersContentViewController.swift */; };
		BD8BC84E2D9101EB00055118 /* FollowingCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD8BC84D2D9101EB00055118 /* FollowingCell.swift */; };
		BD8BC8502D9101F000055118 /* FollowersCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD8BC84F2D9101F000055118 /* FollowersCell.swift */; };
		BD8BC8522D9101F700055118 /* RecommendedUserCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD8BC8512D9101F700055118 /* RecommendedUserCell.swift */; };
		BD8BC8542D910D7C00055118 /* PersonalHomepageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD8BC8532D910D7C00055118 /* PersonalHomepageViewController.swift */; };
		BD8BC8562D91329400055118 /* CreativeCenterViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD8BC8552D91329400055118 /* CreativeCenterViewController.swift */; };
		BD8CA0732DFA64E100040116 /* MusicSelectBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD8CA0722DFA64E100040116 /* MusicSelectBar.swift */; };
		BD900C2A2D9238FF00479E7D /* ModuleCentralViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD900C292D9238FF00479E7D /* ModuleCentralViewController.swift */; };
		BD900C2C2D924B7E00479E7D /* VideoEditingDetailsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD900C2B2D924B7E00479E7D /* VideoEditingDetailsViewController.swift */; };
		************************ /* MapSearchTestViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD9017012DE72E65001201AF /* MapSearchTestViewController.swift */; };
		************************ /* MapSearchResultViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD9017002DE72E65001201AF /* MapSearchResultViewController.swift */; };
		BD9017062DE72EE0001201AF /* MapTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD9017052DE72EE0001201AF /* MapTest.swift */; };
		BD91098B2D8CF68100B66162 /* VideoDeliveryCenterViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* VideoDeliveryCenterViewController.swift */; };
		************************ /* VideoDeliveryCenterSearchViewCotroller.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* VideoDeliveryCenterSearchViewCotroller.swift */; };
		************************ /* ContentManagementViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD91099A2D8D608900B66162 /* ContentManagementViewController.swift */; };
		BD91099D2D8D910A00B66162 /* ApplyOpenShopWindowViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* ApplyOpenShopWindowViewController.swift */; };
		BD91C0002DA4D3FA002003A4 /* ap002.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = BD91BFFF2DA4D3FA002003A4 /* ap002.mp3 */; };
		BD91C0012DA4D3FA002003A4 /* ap001.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = BD91BFFE2DA4D3FA002003A4 /* ap001.mp3 */; };
		BD9270D32DDDC7F700F46AF3 /* UserInfoEditAddressList.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD9270D22DDDC7F700F46AF3 /* UserInfoEditAddressList.swift */; };
		BD9270D92DDDFC1A00F46AF3 /* UserInfoEditAddressSubListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD9270D82DDDFC1A00F46AF3 /* UserInfoEditAddressSubListViewController.swift */; };
		BD92C4632DC0B7240039AAC3 /* AboutUsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* AboutUsViewController.swift */; };
		BD9445F32DCF1DD90006F038 /* LoginViewController2.0.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* LoginViewController2.0.swift */; };
		BD9A0DC82DE15E53007FED9A /* Color+App.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD9A0DC72DE15E53007FED9A /* Color+App.swift */; };
		BD9A0DCA2DE16FBE007FED9A /* VideoHorizontalListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD9A0DC92DE16FBE007FED9A /* VideoHorizontalListCell.swift */; };
		BD9C86912DB5D21B00CED36A /* BeautyPanelView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD9C86902DB5D21B00CED36A /* BeautyPanelView.swift */; };
		BD9CC7112DF7D3BB0011BD68 /* MusicItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD9CC7102DF7D3BB0011BD68 /* MusicItem.swift */; };
		BD9CC7132DF7D6090011BD68 /* AspectRatioOption.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD9CC7122DF7D6090011BD68 /* AspectRatioOption.swift */; };
		BD9CC7162DF7DC7F0011BD68 /* BeautyItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD9CC7152DF7DC7F0011BD68 /* BeautyItem.swift */; };
		BD9CC7182DF7DCAF0011BD68 /* DownloadState.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD9CC7172DF7DCAF0011BD68 /* DownloadState.swift */; };
		BD9CC71A2DF7DDF40011BD68 /* VideoRecordViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD9CC7192DF7DDF40011BD68 /* VideoRecordViewModel.swift */; };
		BDA7BDD42DA7CCFA008DE990 /* TXUGCPublishOptCenter.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDCF2DA7CCFA008DE990 /* TXUGCPublishOptCenter.m */; };
		BDA7BDD52DA7CCFA008DE990 /* TXUGCPublish.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDCD2DA7CCFA008DE990 /* TXUGCPublish.m */; };
		BDA7BDD62DA7CCFA008DE990 /* TVCHttpMessageURLProtocol.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDC62DA7CCFA008DE990 /* TVCHttpMessageURLProtocol.m */; };
		BDA7BDD72DA7CCFA008DE990 /* TVCReport.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDCA2DA7CCFA008DE990 /* TVCReport.m */; };
		BDA7BDD82DA7CCFA008DE990 /* TVCConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDC12DA7CCFA008DE990 /* TVCConfig.m */; };
		BDA7BDD92DA7CCFA008DE990 /* UploadResumeDefaultController.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDD22DA7CCFA008DE990 /* UploadResumeDefaultController.m */; };
		BDA7BDDA2DA7CCFA008DE990 /* TVCCommon.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDBF2DA7CCFA008DE990 /* TVCCommon.m */; };
		BDA7BDDB2DA7CCFA008DE990 /* QuicClient.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDB92DA7CCFA008DE990 /* QuicClient.m */; };
		BDA7BDDC2DA7CCFA008DE990 /* TVCCOSXMLEndPoint.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDC32DA7CCFA008DE990 /* TVCCOSXMLEndPoint.m */; };
		BDA7BDDD2DA7CCFA008DE990 /* TVCLog.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDC82DA7CCFA008DE990 /* TVCLog.m */; };
		BDA7BDDE2DA7CCFA008DE990 /* TVCClient.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDBB2DA7CCFA008DE990 /* TVCClient.m */; };
		BDA7BDDF2DA7CCFA008DE990 /* TXUGCPublishUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDD12DA7CCFA008DE990 /* TXUGCPublishUtil.m */; };
		BDA7BDE02DA7CCFA008DE990 /* TVCQuicConfigProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDB02DA7CCFA008DE990 /* TVCQuicConfigProxy.m */; };
		BDA7BDE12DA7CCFA008DE990 /* TVCUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDCC2DA7CCFA008DE990 /* TVCUtils.m */; };
		BDA7BDE22DA7CCFA008DE990 /* TVCClientInner.m in Sources */ = {isa = PBXBuildFile; fileRef = BDA7BDBD2DA7CCFA008DE990 /* TVCClientInner.m */; };
		BDA955222DB10FEA00344770 /* WebViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDA955212DB10FEA00344770 /* WebViewController.swift */; };
		BDA9BA5E2DCA091A00621DCA /* UIView+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDA9BA5C2DCA091A00621DCA /* UIView+Ext.swift */; };
		BDA9BA5F2DCA091A00621DCA /* CALayer+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDA9BA592DCA091A00621DCA /* CALayer+Ext.swift */; };
		BDA9BA602DCA091A00621DCA /* String+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDA9BA5A2DCA091A00621DCA /* String+Ext.swift */; };
		BDA9BA612DCA091A00621DCA /* UIWindow+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDA9BA5D2DCA091A00621DCA /* UIWindow+Ext.swift */; };
		BDA9BA622DCA091A00621DCA /* UITableView+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDA9BA5B2DCA091A00621DCA /* UITableView+Ext.swift */; };
		BDA9BA642DCA09E400621DCA /* NavigationController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDA9BA632DCA09E400621DCA /* NavigationController.swift */; };
		BDAAAA112DC99AE2007665C7 /* CleanupConfirmAlertView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDAAAA102DC99AE2007665C7 /* CleanupConfirmAlertView.swift */; };
		BDAAAA132DC99CB7007665C7 /* CleanupFooterView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDAAAA122DC99CB7007665C7 /* CleanupFooterView.swift */; };
		BDAAAA152DC99D2C007665C7 /* SmallVideoItemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDAAAA142DC99D2C007665C7 /* SmallVideoItemCell.swift */; };
		BDAC748C2DAA608B0053D2A2 /* VerifyMobilePhoneNumberViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDAC748B2DAA608B0053D2A2 /* VerifyMobilePhoneNumberViewController.swift */; };
		BDAE9B372DDDB0BC00AB0C50 /* UserInfoEditBirthdayViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDAE9B362DDDB0BC00AB0C50 /* UserInfoEditBirthdayViewController.swift */; };
		BDB5C8FE2D938FE400B2DCFE /* VideoDisplayCenterViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDB5C8FD2D938FE400B2DCFE /* VideoDisplayCenterViewController.swift */; };
		BDB944012D8BA7D900AAF5BD /* HiddenNavController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDB944002D8BA7D900AAF5BD /* HiddenNavController.swift */; };
		BDB944032D8BAE1200AAF5BD /* AccountSettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDB944022D8BAE1200AAF5BD /* AccountSettingViewController.swift */; };
		BDB944052D8BB76100AAF5BD /* AccountPrivacyViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDB944042D8BB76100AAF5BD /* AccountPrivacyViewController.swift */; };
		BDB944072D8BB87700AAF5BD /* DeviceManagementViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDB944062D8BB87700AAF5BD /* DeviceManagementViewController.swift */; };
		BDB944092D8BB94500AAF5BD /* PhoneBindingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDB944082D8BB94500AAF5BD /* PhoneBindingViewController.swift */; };
		BDB9440B2D8BBD7100AAF5BD /* BlacklistManagerViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDB9440A2D8BBD7100AAF5BD /* BlacklistManagerViewController.swift */; };
		BDB9440E2D8BC53D00AAF5BD /* UIWindowExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDB9440C2D8BC53D00AAF5BD /* UIWindowExtension.swift */; };
		BDB944102D8BED9700AAF5BD /* MyCollectionViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDB9440F2D8BED9700AAF5BD /* MyCollectionViewController.swift */; };
		BDB944122D8BF04700AAF5BD /* NotificationSettingsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDB944112D8BF04700AAF5BD /* NotificationSettingsViewController.swift */; };
		BDB9D1082E17E078004AB207 /* DeleteAccountViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDB9D1072E17E078004AB207 /* DeleteAccountViewController.swift */; };
		BDBFCEB62DDB347800278842 /* UserInfoEditGenderViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDBFCEB52DDB347800278842 /* UserInfoEditGenderViewController.swift */; };
		BDBFCEB82DDB4CE800278842 /* UserInfoEditSignatureViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDBFCEB72DDB4CE800278842 /* UserInfoEditSignatureViewController.swift */; };
		BDBFCEBA2DDB5D6100278842 /* UserInfoEditOccupationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDBFCEB92DDB5D6100278842 /* UserInfoEditOccupationViewController.swift */; };
		BDC195302D97836A00318532 /* CoreTelephony.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BDC1952F2D97836A00318532 /* CoreTelephony.framework */; };
		BDC195322D97867600318532 /* VideoRecordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDC195312D97867600318532 /* VideoRecordViewController.swift */; };
		BDC195352D978D6800318532 /* CoreMotion.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BDC195342D978D6800318532 /* CoreMotion.framework */; };
		BDC195362D9795B400318532 /* TXFFmpeg.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD0048392D96D02D001BD612 /* TXFFmpeg.xcframework */; };
		BDC195372D9795B400318532 /* TXFFmpeg.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = BD0048392D96D02D001BD612 /* TXFFmpeg.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		BDC195382D9795B400318532 /* TXLiteAVSDK_UGC.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD00483A2D96D02D001BD612 /* TXLiteAVSDK_UGC.xcframework */; };
		BDC195392D9795B400318532 /* TXLiteAVSDK_UGC.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = BD00483A2D96D02D001BD612 /* TXLiteAVSDK_UGC.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		BDC1953A2D9795B400318532 /* TXSoundTouch.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD00483B2D96D02D001BD612 /* TXSoundTouch.xcframework */; };
		BDC1953B2D9795B400318532 /* TXSoundTouch.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = BD00483B2D96D02D001BD612 /* TXSoundTouch.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		BDC2FC282E28CE9300444A56 /* LocationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDC2FC262E28CE9300444A56 /* LocationManager.swift */; };
		BDC2FD882E28ECED00444A56 /* CommentActionSheet.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDC2FD872E28ECED00444A56 /* CommentActionSheet.swift */; };
		BDC780882DBA0B8100177DD8 /* Network.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BDC780872DBA0B8100177DD8 /* Network.framework */; };
		BDC7808B2DBA0BF800177DD8 /* ATAuthSDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BDC7808A2DBA0BF800177DD8 /* ATAuthSDK.framework */; };
		BDC7808E2DBA0C0B00177DD8 /* YTXOperators.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BDC7808D2DBA0C0B00177DD8 /* YTXOperators.framework */; };
		BDC7808F2DBA0C0B00177DD8 /* YTXMonitor.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BDC7808C2DBA0C0B00177DD8 /* YTXMonitor.framework */; };
		BDC780912DBA1EF100177DD8 /* SameCityViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDC780902DBA1EF100177DD8 /* SameCityViewController.swift */; };
		BDC7D5E82DE98DB100F75FCA /* AspectRatioSelectorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDC7D5E72DE98DB100F75FCA /* AspectRatioSelectorView.swift */; };
		BDC9A0712DD5D636001ED626 /* UserInformationEditingPage.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDC9A0702DD5D636001ED626 /* UserInformationEditingPage.swift */; };
		BDC9A0732DD5ED0C001ED626 /* UserInfoEditNameViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDC9A0722DD5ED0C001ED626 /* UserInfoEditNameViewController.swift */; };
		BDC9A0752DD5F599001ED626 /* UserInfoEditNumViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDC9A0742DD5F599001ED626 /* UserInfoEditNumViewController.swift */; };
		BDCB80DE2D94DDC100725076 /* LoginViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDCB80DD2D94DDC100725076 /* LoginViewController.swift */; };
		BDCB80E22D94F1F200725076 /* VideoCommentViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDCB80E12D94F1F200725076 /* VideoCommentViewController.swift */; };
		BDCB80E62D94F37A00725076 /* CommentCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDCB80E52D94F37A00725076 /* CommentCell.swift */; };
		BDCEB3182E2B3354000CFB0F /* WeChatMiniProgramManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDCEB3172E2B3354000CFB0F /* WeChatMiniProgramManager.swift */; };
		BDD1DE782DA3A15A00E39244 /* VideoEditTestViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDD1DE772DA3A15A00E39244 /* VideoEditTestViewController.swift */; };
		BDD363CE2DF194E300C0E56A /* TimerSelectorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDD363CD2DF194E300C0E56A /* TimerSelectorView.swift */; };
		BDD4C9672D91451500478311 /* VideoDraftBoxViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDD4C9662D91451500478311 /* VideoDraftBoxViewController.swift */; };
		BDD56B302D896A7000F805C3 /* LaunchScreenV2.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = BDD56B2E2D896A7000F805C3 /* LaunchScreenV2.storyboard */; };
		BDD58DF12DFBCFD600C26284 /* YearPickerPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDD58DF02DFBCFD600C26284 /* YearPickerPopupView.swift */; };
		BDD58DF32DFBD06000C26284 /* CategoryPickerPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDD58DF22DFBD06000C26284 /* CategoryPickerPopupView.swift */; };
		BDDC074D2E0E2916000D0903 /* PlayerTestVC.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDDC074C2E0E2916000D0903 /* PlayerTestVC.swift */; };
		BDE5E8AE2DD21B85006A913A /* FilterPanelView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDE5E8AD2DD21B85006A913A /* FilterPanelView.swift */; };
		BDE5E8B22DD21C5A006A913A /* FilterConfigItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDE5E8AF2DD21C5A006A913A /* FilterConfigItem.swift */; };
		BDE8E1FC2DE846AF0082E126 /* WechatOpenSDK-XCFramework.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = BD8751272DE8356100C75E45 /* WechatOpenSDK-XCFramework.xcframework */; };
		BDE8E1FD2DE846AF0082E126 /* WechatOpenSDK-XCFramework.xcframework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = BD8751272DE8356100C75E45 /* WechatOpenSDK-XCFramework.xcframework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		BDE8E1FF2DE8693B0082E126 /* ShareSheetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDE8E1FE2DE8693B0082E126 /* ShareSheetView.swift */; };
		BDEF11112D895E5000583203 /* BaseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDEF11102D895E5000583203 /* BaseViewController.swift */; };
		BDF701642E2DEDC60065C7D8 /* InfoPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDF701622E2DEDC60065C7D8 /* InfoPopupView.swift */; };
		BDF701672E2E383D0065C7D8 /* BackInterceptPopupView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDF701652E2E383D0065C7D8 /* BackInterceptPopupView.swift */; };
		BDFBD89E2E20A7C600F50798 /* CommentReplyCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDFBD89D2E20A7C600F50798 /* CommentReplyCell.swift */; };
		BDFC95A62DCC435E002D2FFC /* StatsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDFC95A42DCC435E002D2FFC /* StatsView.swift */; };
		BDFC95A82DCC435E002D2FFC /* CollectionsListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDFC95A02DCC435E002D2FFC /* CollectionsListViewController.swift */; };
		BDFC95AA2DCC435E002D2FFC /* HomepageListProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDFC95A12DCC435E002D2FFC /* HomepageListProtocol.swift */; };
		BDFC95CE2DCC4A44002D2FFC /* VideoListController.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDFC95CA2DCC4A44002D2FFC /* VideoListController.swift */; };
		BDFC95D02DCC4A44002D2FFC /* PageHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDFC95C52DCC4A44002D2FFC /* PageHeaderView.swift */; };
		BDFC95D22DCC4A44002D2FFC /* PageContainScrollView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDFC95C42DCC4A44002D2FFC /* PageContainScrollView.swift */; };
		BDFC95D32DCC4A44002D2FFC /* RollingTools.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDFC95C82DCC4A44002D2FFC /* RollingTools.swift */; };
		BDFC95D42DCC4A44002D2FFC /* PageSegmentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDFC95C62DCC4A44002D2FFC /* PageSegmentView.swift */; };
		BDFC95EE2DCC4BCB002D2FFC /* VideoViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = BDFC95ED2DCC4BCB002D2FFC /* VideoViewCell.swift */; };
		F49BA161121CC98A956A3F09 /* Pods_Shuxiaoqi.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DC01EA41A40410852670D7CA /* Pods_Shuxiaoqi.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		04ABF10C2D89040400E85541 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 04ABF0ED2D89040100E85541 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 04ABF0F42D89040100E85541;
			remoteInfo = Shuxiaoqi;
		};
		04ABF1162D89040400E85541 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 04ABF0ED2D89040100E85541 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 04ABF0F42D89040100E85541;
			remoteInfo = Shuxiaoqi;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		BDC1953C2D9795B400318532 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				BDC1953B2D9795B400318532 /* TXSoundTouch.xcframework in Embed Frameworks */,
				BDC195372D9795B400318532 /* TXFFmpeg.xcframework in Embed Frameworks */,
				BDE8E1FD2DE846AF0082E126 /* WechatOpenSDK-XCFramework.xcframework in Embed Frameworks */,
				BDC195392D9795B400318532 /* TXLiteAVSDK_UGC.xcframework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		04ABF0F52D89040100E85541 /* Shuxiaoqi.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Shuxiaoqi.app; sourceTree = BUILT_PRODUCTS_DIR; };
		04ABF0F82D89040100E85541 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		04ABF0FA2D89040100E85541 /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		04ABF1012D89040400E85541 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		04ABF1062D89040400E85541 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		04ABF10B2D89040400E85541 /* ShuxiaoqiTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ShuxiaoqiTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		04ABF10F2D89040400E85541 /* ShuxiaoqiTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShuxiaoqiTests.swift; sourceTree = "<group>"; };
		04ABF1152D89040400E85541 /* ShuxiaoqiUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ShuxiaoqiUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		04ABF1192D89040400E85541 /* ShuxiaoqiUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShuxiaoqiUITests.swift; sourceTree = "<group>"; };
		04ABF11B2D89040400E85541 /* ShuxiaoqiUITestsLaunchTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShuxiaoqiUITestsLaunchTests.swift; sourceTree = "<group>"; };
		A3CF7779FFF197B3D4A29621 /* Pods-Shuxiaoqi.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Shuxiaoqi.debug.xcconfig"; path = "Target Support Files/Pods-Shuxiaoqi/Pods-Shuxiaoqi.debug.xcconfig"; sourceTree = "<group>"; };
		BD00482E2D963637001BD612 /* APIResponse.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIResponse.swift; sourceTree = "<group>"; };
		BD00482F2D963637001BD612 /* APIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIService.swift; sourceTree = "<group>"; };
		BD0048302D963637001BD612 /* NetworkManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkManager.swift; sourceTree = "<group>"; };
		BD0048392D96D02D001BD612 /* TXFFmpeg.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = TXFFmpeg.xcframework; sourceTree = "<group>"; };
		BD00483A2D96D02D001BD612 /* TXLiteAVSDK_UGC.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = TXLiteAVSDK_UGC.xcframework; sourceTree = "<group>"; };
		BD00483B2D96D02D001BD612 /* TXSoundTouch.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = TXSoundTouch.xcframework; sourceTree = "<group>"; };
		BD00483F2D96D174001BD612 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		BD0048412D96D17D001BD612 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		BD0048432D96D188001BD612 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		BD0048452D96D192001BD612 /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
		BD0048472D96D19D001BD612 /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		BD0048492D96D1A6001BD612 /* VideoToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = VideoToolbox.framework; path = System/Library/Frameworks/VideoToolbox.framework; sourceTree = SDKROOT; };
		BD00484B2D96D1AB001BD612 /* ReplayKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ReplayKit.framework; path = System/Library/Frameworks/ReplayKit.framework; sourceTree = SDKROOT; };
		BD00484D2D96D1B0001BD612 /* GLKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = GLKit.framework; path = System/Library/Frameworks/GLKit.framework; sourceTree = SDKROOT; };
		BD00484F2D96D1B6001BD612 /* OpenAL.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenAL.framework; path = System/Library/Frameworks/OpenAL.framework; sourceTree = SDKROOT; };
		BD0048512D96D1BD001BD612 /* CoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreServices.framework; path = System/Library/Frameworks/CoreServices.framework; sourceTree = SDKROOT; };
		BD005AFB2DF2D29700F2A1AF /* ProfileHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileHeaderView.swift; sourceTree = "<group>"; };
		BD02D4572DFD096000D5D2C9 /* VideoUploadParameterManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoUploadParameterManager.swift; sourceTree = "<group>"; };
		BD0579192DEED84C008C14A2 /* InteractiveInformationViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InteractiveInformationViewController.swift; sourceTree = "<group>"; };
		BD079B0F2DE5898100FB2B57 /* CommissionChangeDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommissionChangeDetailViewController.swift; sourceTree = "<group>"; };
		BD079B112DE5A9B500FB2B57 /* CoinTransactionRecordViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CoinTransactionRecordViewController.swift; sourceTree = "<group>"; };
		************************ /* VideoEditViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoEditViewController.swift; sourceTree = "<group>"; };
		BD08B83A2E33331300FE74E5 /* DeviceUtils.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeviceUtils.swift; sourceTree = "<group>"; };
		BD0BCA4A2DF281F900F47F51 /* NotificationPopSelector.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationPopSelector.swift; sourceTree = "<group>"; };
		BD0BCA4C2DF2870300F47F51 /* VisiblePopSelector.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VisiblePopSelector.swift; sourceTree = "<group>"; };
		BD0BCA4E2DF2913C00F47F51 /* FollowMessageListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FollowMessageListViewController.swift; sourceTree = "<group>"; };
		BD0BCA502DF2943A00F47F51 /* FollowMessageCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FollowMessageCell.swift; sourceTree = "<group>"; };
		BD0BCA512DF2943A00F47F51 /* FollowMessageListViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FollowMessageListViewModel.swift; sourceTree = "<group>"; };
		BD10F3912DF6DCAD00A7E8A4 /* VolumeKeyCaptureManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VolumeKeyCaptureManager.swift; sourceTree = "<group>"; };
		BD10F3952DF7097B00A7E8A4 /* ModeCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ModeCell.swift; sourceTree = "<group>"; };
		BD133C062D9E75C500384522 /* UIViewController+Toast.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIViewController+Toast.swift"; sourceTree = "<group>"; };
		BD133C082D9E804F00384522 /* APIRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIRequest.swift; sourceTree = "<group>"; };
		BD133C0A2D9E805700384522 /* APIRouter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIRouter.swift; sourceTree = "<group>"; };
		BD133C0C2D9E806900384522 /* APIManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIManager.swift; sourceTree = "<group>"; };
		BD136CBE2E17AC0200CEED05 /* UIViewExtensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIViewExtensions.swift; sourceTree = "<group>"; };
		BD136CBF2E17AC0200CEED05 /* VideoDisplayCenterUIComponents.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoDisplayCenterUIComponents.swift; sourceTree = "<group>"; };
		BD136CC02E17AC0200CEED05 /* VideoDisplayCenterViewProtocols.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoDisplayCenterViewProtocols.swift; sourceTree = "<group>"; };
		BD136CC12E17AC0200CEED05 /* VideoPageUIComponents.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoPageUIComponents.swift; sourceTree = "<group>"; };
		BD1EFCAC2E0AB5710079E39B /* NoteEditingDetailsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NoteEditingDetailsViewController.swift; sourceTree = "<group>"; };
		BD1FEC212DD7668E00FC0AC8 /* IconMenuCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IconMenuCell.swift; sourceTree = "<group>"; };
		BD1FEC252DD767E200FC0AC8 /* SectionHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SectionHeaderView.swift; sourceTree = "<group>"; };
		BD1FEC272DD7681800FC0AC8 /* RecommendListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecommendListViewController.swift; sourceTree = "<group>"; };
		BD1FEC292DD7687200FC0AC8 /* NormalListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NormalListViewController.swift; sourceTree = "<group>"; };
		BD1FEC2B2DD768D100FC0AC8 /* DiscoverSearchResultCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DiscoverSearchResultCell.swift; sourceTree = "<group>"; };
		BD1FEC2D2DD768FF00FC0AC8 /* CategorySelectionPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CategorySelectionPopupView.swift; sourceTree = "<group>"; };
		BD1FEC312DD769A700FC0AC8 /* CategoryPopupCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CategoryPopupCell.swift; sourceTree = "<group>"; };
		BD212A9E2DD426A7004329BB /* WebViewJavascriptBridge.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WebViewJavascriptBridge.h; sourceTree = "<group>"; };
		BD212A9F2DD426A7004329BB /* WebViewJavascriptBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WebViewJavascriptBridge.m; sourceTree = "<group>"; };
		BD212AA02DD426A7004329BB /* WebViewJavascriptBridge_JS.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WebViewJavascriptBridge_JS.h; sourceTree = "<group>"; };
		BD212AA12DD426A7004329BB /* WebViewJavascriptBridge_JS.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WebViewJavascriptBridge_JS.m; sourceTree = "<group>"; };
		BD212AA22DD426A7004329BB /* WebViewJavascriptBridgeBase.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WebViewJavascriptBridgeBase.h; sourceTree = "<group>"; };
		BD212AA32DD426A7004329BB /* WebViewJavascriptBridgeBase.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WebViewJavascriptBridgeBase.m; sourceTree = "<group>"; };
		BD212AA42DD426A7004329BB /* WKWebViewJavascriptBridge.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WKWebViewJavascriptBridge.h; sourceTree = "<group>"; };
		BD212AA52DD426A7004329BB /* WKWebViewJavascriptBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WKWebViewJavascriptBridge.m; sourceTree = "<group>"; };
		BD212AAB2DD42ABB004329BB /* Shuxiaoqi-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Shuxiaoqi-Bridging-Header.h"; sourceTree = "<group>"; };
		BD212AAE2DD47756004329BB /* MusicListTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MusicListTableViewCell.swift; sourceTree = "<group>"; };
		BD212AB02DD49C10004329BB /* MusicPanelView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MusicPanelView.swift; sourceTree = "<group>"; };
		************************ /* ExpandRepliesCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExpandRepliesCell.swift; sourceTree = "<group>"; };
		BD3B8FB92D8E4DFE005539BB /* RealNameAuthenticationResultViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RealNameAuthenticationResultViewController.swift; sourceTree = "<group>"; };
		BD3B8FBB2D8EADC6005539BB /* FollowListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FollowListViewController.swift; sourceTree = "<group>"; };
		BD3CDAEF2E0C05D70017EEA3 /* BrowsingHistoryViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BrowsingHistoryViewController.swift; sourceTree = "<group>"; };
		BD431E052DB3366500D9765F /* UserSharingViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserSharingViewController.swift; sourceTree = "<group>"; };
		BD4329792DAD2ABD00188D9B /* GuideViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GuideViewController.swift; sourceTree = "<group>"; };
		BD44476D2E2255B3008BB946 /* MentionParser.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MentionParser.swift; sourceTree = "<group>"; };
		BD4447702E22671E008BB946 /* AuthCoordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthCoordinator.swift; sourceTree = "<group>"; };
		BD4828942DB874230068DBCC /* RecordSettingsPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecordSettingsPopupView.swift; sourceTree = "<group>"; };
		BD4828962DB8758E0068DBCC /* RecordGridView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecordGridView.swift; sourceTree = "<group>"; };
		************************ /* QRScanViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QRScanViewController.swift; sourceTree = "<group>"; };
		BD4A45BB2DDC21A5000C64F9 /* occupations.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = occupations.json; sourceTree = "<group>"; };
		BD4A45BD2DDC5954000C64F9 /* university.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = university.json; sourceTree = "<group>"; };
		BD4A45CC2DDC8553000C64F9 /* UserInfoEditAddressViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoEditAddressViewController.swift; sourceTree = "<group>"; };
		BD4A45CE2DDC8636000C64F9 /* UserInfoEditSchoolViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoEditSchoolViewController.swift; sourceTree = "<group>"; };
		BD4A45D02DDC8F5E000C64F9 /* UserInfoEditSchoolList.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoEditSchoolList.swift; sourceTree = "<group>"; };
		BD4BA84B2E00FF03008D19C7 /* ImageCropPreviewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageCropPreviewController.swift; sourceTree = "<group>"; };
		BD4CE3D12DE04EFB004287BE /* GoodsCheckstandViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GoodsCheckstandViewController.swift; sourceTree = "<group>"; };
		BD4CE3D42DE065B3004287BE /* AddedFollowershipNotificationViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddedFollowershipNotificationViewController.swift; sourceTree = "<group>"; };
		BD4CE3D62DE09406004287BE /* LoginViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginViewModel.swift; sourceTree = "<group>"; };
		BD4CE3D82DE09BA8004287BE /* UILabel+TapExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UILabel+TapExtension.swift"; sourceTree = "<group>"; };
		BD4F03542E03F0EE00326B5D /* UserInfoEditAddressCityListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoEditAddressCityListViewController.swift; sourceTree = "<group>"; };
		BD53A50F2D8958B00064BA5F /* MeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MeViewController.swift; sourceTree = "<group>"; };
		BD53A5112D8958C10064BA5F /* MessageViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessageViewController.swift; sourceTree = "<group>"; };
		BD54967B2D92982500F2F6C5 /* CommentHistoryViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentHistoryViewController.swift; sourceTree = "<group>"; };
		BD5496892D92B6A400F2F6C5 /* CommentHistoryCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentHistoryCell.swift; sourceTree = "<group>"; };
		BD54968B2D92B6AA00F2F6C5 /* ReplyCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReplyCell.swift; sourceTree = "<group>"; };
		BD57CE772D9A9D8800052EE1 /* VideoSDKTestViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoSDKTestViewController.swift; sourceTree = "<group>"; };
		************************ /* HomeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeViewController.swift; sourceTree = "<group>"; };
		BD5AC9EE2DBC7D5F00350A6B /* ProductItemCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProductItemCell.swift; sourceTree = "<group>"; };
		BD5AC9F12DBC7E2500350A6B /* DiscoverProductCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DiscoverProductCell.swift; sourceTree = "<group>"; };
		BD5AC9F32DBC7E6200350A6B /* VideoCollectionCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoCollectionCell.swift; sourceTree = "<group>"; };
		BD5AC9F72DBCD39100350A6B /* RASManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RASManager.swift; sourceTree = "<group>"; };
		BD6560BE2E1CEE6D00084C6C /* NoteCarouselView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NoteCarouselView.swift; sourceTree = "<group>"; };
		BD666FD62DC1BF8B0081F608 /* ProfileCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileCell.swift; sourceTree = "<group>"; };
		BD666FD82DC1BFCA0081F608 /* FunctionsCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FunctionsCell.swift; sourceTree = "<group>"; };
		BD666FDA2DC1BFE00081F608 /* ContentCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentCell.swift; sourceTree = "<group>"; };
		BD666FDC2DC1C0080081F608 /* ContentItemCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentItemCell.swift; sourceTree = "<group>"; };
		BD66ED642E0FEAE000E2C690 /* ShareEvent.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShareEvent.swift; sourceTree = "<group>"; };
		************************ /* VideoItemCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoItemCell.swift; sourceTree = "<group>"; };
		BD695C492DF04CA8004A2F0F /* CommonAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommonAlertView.swift; sourceTree = "<group>"; };
		BD718A482E0D377300C781A6 /* APIManager+Search.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "APIManager+Search.swift"; sourceTree = "<group>"; };
		BD7223DA2D8A5D5700496130 /* UIColor+Hex.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIColor+Hex.swift"; sourceTree = "<group>"; };
		BD7223DD2D8A602100496130 /* UIView+Gradient.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIView+Gradient.swift"; sourceTree = "<group>"; };
		BD7223DF2D8A62D500496130 /* CustomTabBarController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomTabBarController.swift; sourceTree = "<group>"; };
		BD7223E12D8A66DD00496130 /* FriendViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FriendViewController.swift; sourceTree = "<group>"; };
		************************ /* SetNewPasswordViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SetNewPasswordViewController.swift; sourceTree = "<group>"; };
		************************ /* UpgradePopupViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpgradePopupViewController.swift; sourceTree = "<group>"; };
		************************ /* PreferenceTagsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreferenceTagsViewController.swift; sourceTree = "<group>"; };
		BD7D429F2DE3FDE7007986FE /* CreativeVideoItemCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreativeVideoItemCell.swift; sourceTree = "<group>"; };
		BD7D42A12DE4876A007986FE /* LikedWorksViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LikedWorksViewController.swift; sourceTree = "<group>"; };
		************************ /* DiscoverViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DiscoverViewController.swift; sourceTree = "<group>"; };
		************************ /* StoreViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StoreViewController.swift; sourceTree = "<group>"; };
		************************ /* AttentionViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AttentionViewController.swift; sourceTree = "<group>"; };
		************************ /* LeftMenuViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LeftMenuViewController.swift; sourceTree = "<group>"; };
		************************ /* SettingViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingViewController.swift; sourceTree = "<group>"; };
		BD8751272DE8356100C75E45 /* WechatOpenSDK-XCFramework.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = "WechatOpenSDK-XCFramework.xcframework"; path = "Pods/WechatOpenSDK-XCFramework/WechatOpenSDK-XCFramework.xcframework"; sourceTree = "<group>"; };
		BD8BC8492D9101DC00055118 /* FollowingContentViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FollowingContentViewController.swift; sourceTree = "<group>"; };
		BD8BC84B2D9101E400055118 /* FollowersContentViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FollowersContentViewController.swift; sourceTree = "<group>"; };
		BD8BC84D2D9101EB00055118 /* FollowingCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FollowingCell.swift; sourceTree = "<group>"; };
		BD8BC84F2D9101F000055118 /* FollowersCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FollowersCell.swift; sourceTree = "<group>"; };
		BD8BC8512D9101F700055118 /* RecommendedUserCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecommendedUserCell.swift; sourceTree = "<group>"; };
		BD8BC8532D910D7C00055118 /* PersonalHomepageViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PersonalHomepageViewController.swift; sourceTree = "<group>"; };
		BD8BC8552D91329400055118 /* CreativeCenterViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreativeCenterViewController.swift; sourceTree = "<group>"; };
		BD8CA0722DFA64E100040116 /* MusicSelectBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MusicSelectBar.swift; sourceTree = "<group>"; };
		BD900C292D9238FF00479E7D /* ModuleCentralViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ModuleCentralViewController.swift; sourceTree = "<group>"; };
		BD900C2B2D924B7E00479E7D /* VideoEditingDetailsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoEditingDetailsViewController.swift; sourceTree = "<group>"; };
		BD9017002DE72E65001201AF /* MapSearchResultViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MapSearchResultViewController.swift; sourceTree = "<group>"; };
		BD9017012DE72E65001201AF /* MapSearchTestViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MapSearchTestViewController.swift; sourceTree = "<group>"; };
		BD9017052DE72EE0001201AF /* MapTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MapTest.swift; sourceTree = "<group>"; };
		************************ /* VideoDeliveryCenterViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoDeliveryCenterViewController.swift; sourceTree = "<group>"; };
		************************ /* VideoDeliveryCenterSearchViewCotroller.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoDeliveryCenterSearchViewCotroller.swift; sourceTree = "<group>"; };
		BD91099A2D8D608900B66162 /* ContentManagementViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentManagementViewController.swift; sourceTree = "<group>"; };
		************************ /* ApplyOpenShopWindowViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ApplyOpenShopWindowViewController.swift; sourceTree = "<group>"; };
		BD91BFFE2DA4D3FA002003A4 /* ap001.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = ap001.mp3; sourceTree = "<group>"; };
		BD91BFFF2DA4D3FA002003A4 /* ap002.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = ap002.mp3; sourceTree = "<group>"; };
		BD9270D22DDDC7F700F46AF3 /* UserInfoEditAddressList.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoEditAddressList.swift; sourceTree = "<group>"; };
		BD9270D82DDDFC1A00F46AF3 /* UserInfoEditAddressSubListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoEditAddressSubListViewController.swift; sourceTree = "<group>"; };
		************************ /* AboutUsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AboutUsViewController.swift; sourceTree = "<group>"; };
		************************ /* LoginViewController2.0.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginViewController2.0.swift; sourceTree = "<group>"; };
		BD9A0DC72DE15E53007FED9A /* Color+App.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Color+App.swift"; sourceTree = "<group>"; };
		BD9A0DC92DE16FBE007FED9A /* VideoHorizontalListCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoHorizontalListCell.swift; sourceTree = "<group>"; };
		BD9C86902DB5D21B00CED36A /* BeautyPanelView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BeautyPanelView.swift; sourceTree = "<group>"; };
		BD9C86922DB5F06300CED36A /* Shuxiaoqi.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Shuxiaoqi.entitlements; sourceTree = "<group>"; };
		BD9CC7102DF7D3BB0011BD68 /* MusicItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MusicItem.swift; sourceTree = "<group>"; };
		BD9CC7122DF7D6090011BD68 /* AspectRatioOption.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AspectRatioOption.swift; sourceTree = "<group>"; };
		BD9CC7152DF7DC7F0011BD68 /* BeautyItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BeautyItem.swift; sourceTree = "<group>"; };
		BD9CC7172DF7DCAF0011BD68 /* DownloadState.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DownloadState.swift; sourceTree = "<group>"; };
		BD9CC7192DF7DDF40011BD68 /* VideoRecordViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoRecordViewModel.swift; sourceTree = "<group>"; };
		BDA7BDAF2DA7CCFA008DE990 /* TVCQuicConfigProxy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TVCQuicConfigProxy.h; sourceTree = "<group>"; };
		BDA7BDB02DA7CCFA008DE990 /* TVCQuicConfigProxy.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TVCQuicConfigProxy.m; sourceTree = "<group>"; };
		BDA7BDB22DA7CCFA008DE990 /* IUploadResumeController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = IUploadResumeController.h; sourceTree = "<group>"; };
		BDA7BDB32DA7CCFA008DE990 /* TXUGCPublish.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TXUGCPublish.h; sourceTree = "<group>"; };
		BDA7BDB42DA7CCFA008DE990 /* TXUGCPublishListener.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TXUGCPublishListener.h; sourceTree = "<group>"; };
		BDA7BDB52DA7CCFA008DE990 /* TXUGCPublishTypeDef.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TXUGCPublishTypeDef.h; sourceTree = "<group>"; };
		BDA7BDB62DA7CCFA008DE990 /* UploadResumeDefaultController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UploadResumeDefaultController.h; sourceTree = "<group>"; };
		BDA7BDB82DA7CCFA008DE990 /* QuicClient.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = QuicClient.h; sourceTree = "<group>"; };
		BDA7BDB92DA7CCFA008DE990 /* QuicClient.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = QuicClient.m; sourceTree = "<group>"; };
		BDA7BDBA2DA7CCFA008DE990 /* TVCClient.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TVCClient.h; sourceTree = "<group>"; };
		BDA7BDBB2DA7CCFA008DE990 /* TVCClient.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TVCClient.m; sourceTree = "<group>"; };
		BDA7BDBC2DA7CCFA008DE990 /* TVCClientInner.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TVCClientInner.h; sourceTree = "<group>"; };
		BDA7BDBD2DA7CCFA008DE990 /* TVCClientInner.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TVCClientInner.m; sourceTree = "<group>"; };
		BDA7BDBE2DA7CCFA008DE990 /* TVCCommon.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TVCCommon.h; sourceTree = "<group>"; };
		BDA7BDBF2DA7CCFA008DE990 /* TVCCommon.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TVCCommon.m; sourceTree = "<group>"; };
		BDA7BDC02DA7CCFA008DE990 /* TVCConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TVCConfig.h; sourceTree = "<group>"; };
		BDA7BDC12DA7CCFA008DE990 /* TVCConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TVCConfig.m; sourceTree = "<group>"; };
		BDA7BDC22DA7CCFA008DE990 /* TVCCOSXMLEndPoint.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TVCCOSXMLEndPoint.h; sourceTree = "<group>"; };
		BDA7BDC32DA7CCFA008DE990 /* TVCCOSXMLEndPoint.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TVCCOSXMLEndPoint.m; sourceTree = "<group>"; };
		BDA7BDC42DA7CCFA008DE990 /* TVCHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TVCHeader.h; sourceTree = "<group>"; };
		BDA7BDC52DA7CCFA008DE990 /* TVCHttpMessageURLProtocol.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TVCHttpMessageURLProtocol.h; sourceTree = "<group>"; };
		BDA7BDC62DA7CCFA008DE990 /* TVCHttpMessageURLProtocol.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TVCHttpMessageURLProtocol.m; sourceTree = "<group>"; };
		BDA7BDC72DA7CCFA008DE990 /* TVCLog.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TVCLog.h; sourceTree = "<group>"; };
		BDA7BDC82DA7CCFA008DE990 /* TVCLog.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TVCLog.m; sourceTree = "<group>"; };
		BDA7BDC92DA7CCFA008DE990 /* TVCReport.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TVCReport.h; sourceTree = "<group>"; };
		BDA7BDCA2DA7CCFA008DE990 /* TVCReport.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TVCReport.m; sourceTree = "<group>"; };
		BDA7BDCB2DA7CCFA008DE990 /* TVCUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TVCUtils.h; sourceTree = "<group>"; };
		BDA7BDCC2DA7CCFA008DE990 /* TVCUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TVCUtils.m; sourceTree = "<group>"; };
		BDA7BDCD2DA7CCFA008DE990 /* TXUGCPublish.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TXUGCPublish.m; sourceTree = "<group>"; };
		BDA7BDCE2DA7CCFA008DE990 /* TXUGCPublishOptCenter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TXUGCPublishOptCenter.h; sourceTree = "<group>"; };
		BDA7BDCF2DA7CCFA008DE990 /* TXUGCPublishOptCenter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TXUGCPublishOptCenter.m; sourceTree = "<group>"; };
		BDA7BDD02DA7CCFA008DE990 /* TXUGCPublishUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TXUGCPublishUtil.h; sourceTree = "<group>"; };
		BDA7BDD12DA7CCFA008DE990 /* TXUGCPublishUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TXUGCPublishUtil.m; sourceTree = "<group>"; };
		BDA7BDD22DA7CCFA008DE990 /* UploadResumeDefaultController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UploadResumeDefaultController.m; sourceTree = "<group>"; };
		BDA955212DB10FEA00344770 /* WebViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebViewController.swift; sourceTree = "<group>"; };
		BDA9BA592DCA091A00621DCA /* CALayer+Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "CALayer+Ext.swift"; sourceTree = "<group>"; };
		BDA9BA5A2DCA091A00621DCA /* String+Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "String+Ext.swift"; sourceTree = "<group>"; };
		BDA9BA5B2DCA091A00621DCA /* UITableView+Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UITableView+Ext.swift"; sourceTree = "<group>"; };
		BDA9BA5C2DCA091A00621DCA /* UIView+Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIView+Ext.swift"; sourceTree = "<group>"; };
		BDA9BA5D2DCA091A00621DCA /* UIWindow+Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIWindow+Ext.swift"; sourceTree = "<group>"; };
		BDA9BA632DCA09E400621DCA /* NavigationController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NavigationController.swift; sourceTree = "<group>"; };
		BDAAAA102DC99AE2007665C7 /* CleanupConfirmAlertView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CleanupConfirmAlertView.swift; sourceTree = "<group>"; };
		BDAAAA122DC99CB7007665C7 /* CleanupFooterView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CleanupFooterView.swift; sourceTree = "<group>"; };
		BDAAAA142DC99D2C007665C7 /* SmallVideoItemCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmallVideoItemCell.swift; sourceTree = "<group>"; };
		BDAC748B2DAA608B0053D2A2 /* VerifyMobilePhoneNumberViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VerifyMobilePhoneNumberViewController.swift; sourceTree = "<group>"; };
		BDAE9B362DDDB0BC00AB0C50 /* UserInfoEditBirthdayViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoEditBirthdayViewController.swift; sourceTree = "<group>"; };
		BDB5C8FD2D938FE400B2DCFE /* VideoDisplayCenterViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoDisplayCenterViewController.swift; sourceTree = "<group>"; };
		BDB944002D8BA7D900AAF5BD /* HiddenNavController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HiddenNavController.swift; sourceTree = "<group>"; };
		BDB944022D8BAE1200AAF5BD /* AccountSettingViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AccountSettingViewController.swift; sourceTree = "<group>"; };
		BDB944042D8BB76100AAF5BD /* AccountPrivacyViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AccountPrivacyViewController.swift; sourceTree = "<group>"; };
		BDB944062D8BB87700AAF5BD /* DeviceManagementViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeviceManagementViewController.swift; sourceTree = "<group>"; };
		BDB944082D8BB94500AAF5BD /* PhoneBindingViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhoneBindingViewController.swift; sourceTree = "<group>"; };
		BDB9440A2D8BBD7100AAF5BD /* BlacklistManagerViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BlacklistManagerViewController.swift; sourceTree = "<group>"; };
		BDB9440C2D8BC53D00AAF5BD /* UIWindowExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIWindowExtension.swift; sourceTree = "<group>"; };
		BDB9440F2D8BED9700AAF5BD /* MyCollectionViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyCollectionViewController.swift; sourceTree = "<group>"; };
		BDB944112D8BF04700AAF5BD /* NotificationSettingsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationSettingsViewController.swift; sourceTree = "<group>"; };
		BDB9D1072E17E078004AB207 /* DeleteAccountViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeleteAccountViewController.swift; sourceTree = "<group>"; };
		BDBFCEB52DDB347800278842 /* UserInfoEditGenderViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoEditGenderViewController.swift; sourceTree = "<group>"; };
		BDBFCEB72DDB4CE800278842 /* UserInfoEditSignatureViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoEditSignatureViewController.swift; sourceTree = "<group>"; };
		BDBFCEB92DDB5D6100278842 /* UserInfoEditOccupationViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoEditOccupationViewController.swift; sourceTree = "<group>"; };
		BDC1952F2D97836A00318532 /* CoreTelephony.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreTelephony.framework; path = System/Library/Frameworks/CoreTelephony.framework; sourceTree = SDKROOT; };
		BDC195312D97867600318532 /* VideoRecordViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoRecordViewController.swift; sourceTree = "<group>"; };
		BDC195342D978D6800318532 /* CoreMotion.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMotion.framework; path = System/Library/Frameworks/CoreMotion.framework; sourceTree = SDKROOT; };
		BDC2FC262E28CE9300444A56 /* LocationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LocationManager.swift; sourceTree = "<group>"; };
		BDC2FD872E28ECED00444A56 /* CommentActionSheet.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentActionSheet.swift; sourceTree = "<group>"; };
		BDC780872DBA0B8100177DD8 /* Network.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Network.framework; path = System/Library/Frameworks/Network.framework; sourceTree = SDKROOT; };
		BDC7808A2DBA0BF800177DD8 /* ATAuthSDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = ATAuthSDK.framework; sourceTree = "<group>"; };
		BDC7808C2DBA0C0B00177DD8 /* YTXMonitor.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTXMonitor.framework; sourceTree = "<group>"; };
		BDC7808D2DBA0C0B00177DD8 /* YTXOperators.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTXOperators.framework; sourceTree = "<group>"; };
		BDC780902DBA1EF100177DD8 /* SameCityViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SameCityViewController.swift; sourceTree = "<group>"; };
		BDC7D5E72DE98DB100F75FCA /* AspectRatioSelectorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AspectRatioSelectorView.swift; sourceTree = "<group>"; };
		BDC9A0702DD5D636001ED626 /* UserInformationEditingPage.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInformationEditingPage.swift; sourceTree = "<group>"; };
		BDC9A0722DD5ED0C001ED626 /* UserInfoEditNameViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoEditNameViewController.swift; sourceTree = "<group>"; };
		BDC9A0742DD5F599001ED626 /* UserInfoEditNumViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserInfoEditNumViewController.swift; sourceTree = "<group>"; };
		BDCB80DD2D94DDC100725076 /* LoginViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginViewController.swift; sourceTree = "<group>"; };
		BDCB80E12D94F1F200725076 /* VideoCommentViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoCommentViewController.swift; sourceTree = "<group>"; };
		BDCB80E52D94F37A00725076 /* CommentCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentCell.swift; sourceTree = "<group>"; };
		BDCEB3172E2B3354000CFB0F /* WeChatMiniProgramManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WeChatMiniProgramManager.swift; sourceTree = "<group>"; };
		BDD1DE772DA3A15A00E39244 /* VideoEditTestViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoEditTestViewController.swift; sourceTree = "<group>"; };
		BDD363CD2DF194E300C0E56A /* TimerSelectorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TimerSelectorView.swift; sourceTree = "<group>"; };
		BDD4C9662D91451500478311 /* VideoDraftBoxViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoDraftBoxViewController.swift; sourceTree = "<group>"; };
		BDD56B2F2D896A7000F805C3 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreenV2.storyboard; sourceTree = "<group>"; };
		BDD58DF02DFBCFD600C26284 /* YearPickerPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YearPickerPopupView.swift; sourceTree = "<group>"; };
		BDD58DF22DFBD06000C26284 /* CategoryPickerPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CategoryPickerPopupView.swift; sourceTree = "<group>"; };
		BDDC074C2E0E2916000D0903 /* PlayerTestVC.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlayerTestVC.swift; sourceTree = "<group>"; };
		BDE5E8AD2DD21B85006A913A /* FilterPanelView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterPanelView.swift; sourceTree = "<group>"; };
		BDE5E8AF2DD21C5A006A913A /* FilterConfigItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilterConfigItem.swift; sourceTree = "<group>"; };
		BDE8E1FE2DE8693B0082E126 /* ShareSheetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShareSheetView.swift; sourceTree = "<group>"; };
		BDEF11102D895E5000583203 /* BaseViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseViewController.swift; sourceTree = "<group>"; };
		BDF701622E2DEDC60065C7D8 /* InfoPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InfoPopupView.swift; sourceTree = "<group>"; };
		BDF701652E2E383D0065C7D8 /* BackInterceptPopupView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BackInterceptPopupView.swift; sourceTree = "<group>"; };
		BDFBD89D2E20A7C600F50798 /* CommentReplyCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommentReplyCell.swift; sourceTree = "<group>"; };
		BDFC95A02DCC435E002D2FFC /* CollectionsListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CollectionsListViewController.swift; sourceTree = "<group>"; };
		BDFC95A12DCC435E002D2FFC /* HomepageListProtocol.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomepageListProtocol.swift; sourceTree = "<group>"; };
		BDFC95A42DCC435E002D2FFC /* StatsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StatsView.swift; sourceTree = "<group>"; };
		BDFC95C42DCC4A44002D2FFC /* PageContainScrollView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PageContainScrollView.swift; sourceTree = "<group>"; };
		BDFC95C52DCC4A44002D2FFC /* PageHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PageHeaderView.swift; sourceTree = "<group>"; };
		BDFC95C62DCC4A44002D2FFC /* PageSegmentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PageSegmentView.swift; sourceTree = "<group>"; };
		BDFC95C82DCC4A44002D2FFC /* RollingTools.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RollingTools.swift; sourceTree = "<group>"; };
		BDFC95CA2DCC4A44002D2FFC /* VideoListController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoListController.swift; sourceTree = "<group>"; };
		BDFC95ED2DCC4BCB002D2FFC /* VideoViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoViewCell.swift; sourceTree = "<group>"; };
		C3E5D272A67060B18C2EBECB /* Pods-Shuxiaoqi.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Shuxiaoqi.release.xcconfig"; path = "Target Support Files/Pods-Shuxiaoqi/Pods-Shuxiaoqi.release.xcconfig"; sourceTree = "<group>"; };
		DC01EA41A40410852670D7CA /* Pods_Shuxiaoqi.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Shuxiaoqi.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		************************ /* Search */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Search; sourceTree = "<group>"; };
		BD7189EA2E0CF4E000C781A6 /* Model */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Model; sourceTree = "<group>"; };
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		04ABF0F22D89040100E85541 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BDC780882DBA0B8100177DD8 /* Network.framework in Frameworks */,
				BDC195352D978D6800318532 /* CoreMotion.framework in Frameworks */,
				BDE8E1FC2DE846AF0082E126 /* WechatOpenSDK-XCFramework.xcframework in Frameworks */,
				BDC195302D97836A00318532 /* CoreTelephony.framework in Frameworks */,
				BD0048522D96D1BD001BD612 /* CoreServices.framework in Frameworks */,
				BDC1953A2D9795B400318532 /* TXSoundTouch.xcframework in Frameworks */,
				BD0048502D96D1B6001BD612 /* OpenAL.framework in Frameworks */,
				BDC195362D9795B400318532 /* TXFFmpeg.xcframework in Frameworks */,
				BD00484E2D96D1B0001BD612 /* GLKit.framework in Frameworks */,
				BD00484C2D96D1AB001BD612 /* ReplayKit.framework in Frameworks */,
				BDC195382D9795B400318532 /* TXLiteAVSDK_UGC.xcframework in Frameworks */,
				BD00484A2D96D1A6001BD612 /* VideoToolbox.framework in Frameworks */,
				BD0048482D96D19D001BD612 /* MetalKit.framework in Frameworks */,
				BD0048462D96D192001BD612 /* libsqlite3.tbd in Frameworks */,
				BD0048442D96D188001BD612 /* libc++.tbd in Frameworks */,
				BDC7808E2DBA0C0B00177DD8 /* YTXOperators.framework in Frameworks */,
				BDC7808F2DBA0C0B00177DD8 /* YTXMonitor.framework in Frameworks */,
				BD0048422D96D17D001BD612 /* SystemConfiguration.framework in Frameworks */,
				BD0048402D96D174001BD612 /* Accelerate.framework in Frameworks */,
				BDC7808B2DBA0BF800177DD8 /* ATAuthSDK.framework in Frameworks */,
				BD00483C2D96D02D001BD612 /* TXLiteAVSDK_UGC.xcframework in Frameworks */,
				BD00483D2D96D02D001BD612 /* TXFFmpeg.xcframework in Frameworks */,
				BD00483E2D96D02D001BD612 /* TXSoundTouch.xcframework in Frameworks */,
				F49BA161121CC98A956A3F09 /* Pods_Shuxiaoqi.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04ABF1082D89040400E85541 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04ABF1122D89040400E85541 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		04ABF0EC2D89040100E85541 = {
			isa = PBXGroup;
			children = (
				04ABF0F72D89040100E85541 /* Shuxiaoqi */,
				04ABF10E2D89040400E85541 /* ShuxiaoqiTests */,
				04ABF1182D89040400E85541 /* ShuxiaoqiUITests */,
				04ABF0F62D89040100E85541 /* Products */,
				FF5FFA0D3091F9CE1CD22CE3 /* Pods */,
				F3B695FA91CB6D7F488D0BD2 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		04ABF0F62D89040100E85541 /* Products */ = {
			isa = PBXGroup;
			children = (
				04ABF0F52D89040100E85541 /* Shuxiaoqi.app */,
				04ABF10B2D89040400E85541 /* ShuxiaoqiTests.xctest */,
				04ABF1152D89040400E85541 /* ShuxiaoqiUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		04ABF0F72D89040100E85541 /* Shuxiaoqi */ = {
			isa = PBXGroup;
			children = (
				BDF701632E2DEDC60065C7D8 /* Components */,
				BD9C86922DB5F06300CED36A /* Shuxiaoqi.entitlements */,
				BD5496942D92B7D400F2F6C5 /* Common */,
				BDE5E8B12DD21C5A006A913A /* Models */,
				BD54967D2D92B53300F2F6C5 /* Modules */,
				BD5969E52D8952C8001194A9 /* Base */,
				04ABF0F82D89040100E85541 /* AppDelegate.swift */,
				BD08B83B2E33331300FE74E5 /* Utilities */,
				BD212AAB2DD42ABB004329BB /* Shuxiaoqi-Bridging-Header.h */,
				04ABF0FA2D89040100E85541 /* SceneDelegate.swift */,
				BDD56B2E2D896A7000F805C3 /* LaunchScreenV2.storyboard */,
				04ABF1012D89040400E85541 /* Assets.xcassets */,
				04ABF1062D89040400E85541 /* Info.plist */,
			);
			path = Shuxiaoqi;
			sourceTree = "<group>";
		};
		04ABF10E2D89040400E85541 /* ShuxiaoqiTests */ = {
			isa = PBXGroup;
			children = (
				04ABF10F2D89040400E85541 /* ShuxiaoqiTests.swift */,
			);
			path = ShuxiaoqiTests;
			sourceTree = "<group>";
		};
		04ABF1182D89040400E85541 /* ShuxiaoqiUITests */ = {
			isa = PBXGroup;
			children = (
				04ABF1192D89040400E85541 /* ShuxiaoqiUITests.swift */,
				04ABF11B2D89040400E85541 /* ShuxiaoqiUITestsLaunchTests.swift */,
			);
			path = ShuxiaoqiUITests;
			sourceTree = "<group>";
		};
		BD0048322D963637001BD612 /* NetworkManager */ = {
			isa = PBXGroup;
			children = (
				BD718A482E0D377300C781A6 /* APIManager+Search.swift */,
				BD133C0C2D9E806900384522 /* APIManager.swift */,
				BD133C0A2D9E805700384522 /* APIRouter.swift */,
				BD133C082D9E804F00384522 /* APIRequest.swift */,
				BD00482E2D963637001BD612 /* APIResponse.swift */,
				BD00482F2D963637001BD612 /* APIService.swift */,
				BD0048302D963637001BD612 /* NetworkManager.swift */,
				BD5AC9F72DBCD39100350A6B /* RASManager.swift */,
			);
			path = NetworkManager;
			sourceTree = "<group>";
		};
		BD0048382D96D020001BD612 /* TXSDK */ = {
			isa = PBXGroup;
			children = (
				BDA7BDD32DA7CCFA008DE990 /* upload */,
				BD91BFFE2DA4D3FA002003A4 /* ap001.mp3 */,
				BD91BFFF2DA4D3FA002003A4 /* ap002.mp3 */,
				BD0048392D96D02D001BD612 /* TXFFmpeg.xcframework */,
				BD00483A2D96D02D001BD612 /* TXLiteAVSDK_UGC.xcframework */,
				BD00483B2D96D02D001BD612 /* TXSoundTouch.xcframework */,
			);
			path = TXSDK;
			sourceTree = "<group>";
		};
		BD079B0E2DE588EB00FB2B57 /* Test */ = {
			isa = PBXGroup;
			children = (
				BD079B0F2DE5898100FB2B57 /* CommissionChangeDetailViewController.swift */,
				BD079B112DE5A9B500FB2B57 /* CoinTransactionRecordViewController.swift */,
				BD0579192DEED84C008C14A2 /* InteractiveInformationViewController.swift */,
				BD0BCA4E2DF2913C00F47F51 /* FollowMessageListViewController.swift */,
				BD0BCA502DF2943A00F47F51 /* FollowMessageCell.swift */,
				BD0BCA512DF2943A00F47F51 /* FollowMessageListViewModel.swift */,
			);
			path = Test;
			sourceTree = "<group>";
		};
		BD08B83B2E33331300FE74E5 /* Utilities */ = {
			isa = PBXGroup;
			children = (
				BD08B83A2E33331300FE74E5 /* DeviceUtils.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		BD0BCA492DF281E400F47F51 /* PopView */ = {
			isa = PBXGroup;
			children = (
				BD0BCA4A2DF281F900F47F51 /* NotificationPopSelector.swift */,
				BD0BCA4C2DF2870300F47F51 /* VisiblePopSelector.swift */,
			);
			path = PopView;
			sourceTree = "<group>";
		};
		BD1FEC222DD7668E00FC0AC8 /* Cells */ = {
			isa = PBXGroup;
			children = (
				BD9A0DC92DE16FBE007FED9A /* VideoHorizontalListCell.swift */,
				BD1FEC212DD7668E00FC0AC8 /* IconMenuCell.swift */,
				BD1FEC252DD767E200FC0AC8 /* SectionHeaderView.swift */,
				BD1FEC272DD7681800FC0AC8 /* RecommendListViewController.swift */,
				BD1FEC292DD7687200FC0AC8 /* NormalListViewController.swift */,
				BD1FEC2B2DD768D100FC0AC8 /* DiscoverSearchResultCell.swift */,
				BD1FEC2D2DD768FF00FC0AC8 /* CategorySelectionPopupView.swift */,
				BD1FEC312DD769A700FC0AC8 /* CategoryPopupCell.swift */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		BD1FEC232DD7668E00FC0AC8 /* Discover */ = {
			isa = PBXGroup;
			children = (
				BD1FEC222DD7668E00FC0AC8 /* Cells */,
			);
			path = Discover;
			sourceTree = "<group>";
		};
		BD20A8392DC1C7AC00562E78 /* Comment */ = {
			isa = PBXGroup;
			children = (
				BD5496892D92B6A400F2F6C5 /* CommentHistoryCell.swift */,
				BD54968B2D92B6AA00F2F6C5 /* ReplyCell.swift */,
			);
			path = Comment;
			sourceTree = "<group>";
		};
		BD212AA62DD426A7004329BB /* WebViewJavascriptBridge */ = {
			isa = PBXGroup;
			children = (
				BD212A9E2DD426A7004329BB /* WebViewJavascriptBridge.h */,
				BD212A9F2DD426A7004329BB /* WebViewJavascriptBridge.m */,
				BD212AA02DD426A7004329BB /* WebViewJavascriptBridge_JS.h */,
				BD212AA12DD426A7004329BB /* WebViewJavascriptBridge_JS.m */,
				BD212AA22DD426A7004329BB /* WebViewJavascriptBridgeBase.h */,
				BD212AA32DD426A7004329BB /* WebViewJavascriptBridgeBase.m */,
				BD212AA42DD426A7004329BB /* WKWebViewJavascriptBridge.h */,
				BD212AA52DD426A7004329BB /* WKWebViewJavascriptBridge.m */,
			);
			path = WebViewJavascriptBridge;
			sourceTree = "<group>";
		};
		BD402B862DFFEDA800F6F54E /* UserInfoEdit */ = {
			isa = PBXGroup;
			children = (
				BDC9A0702DD5D636001ED626 /* UserInformationEditingPage.swift */,
				BDC9A0722DD5ED0C001ED626 /* UserInfoEditNameViewController.swift */,
				BDC9A0742DD5F599001ED626 /* UserInfoEditNumViewController.swift */,
				BDBFCEB52DDB347800278842 /* UserInfoEditGenderViewController.swift */,
				BDBFCEB72DDB4CE800278842 /* UserInfoEditSignatureViewController.swift */,
				BDBFCEB92DDB5D6100278842 /* UserInfoEditOccupationViewController.swift */,
				BD4A45CC2DDC8553000C64F9 /* UserInfoEditAddressViewController.swift */,
				BD4A45CE2DDC8636000C64F9 /* UserInfoEditSchoolViewController.swift */,
				BDD58DF02DFBCFD600C26284 /* YearPickerPopupView.swift */,
				BDAE9B362DDDB0BC00AB0C50 /* UserInfoEditBirthdayViewController.swift */,
				BD4A45D02DDC8F5E000C64F9 /* UserInfoEditSchoolList.swift */,
				BD9270D22DDDC7F700F46AF3 /* UserInfoEditAddressList.swift */,
				BD9270D82DDDFC1A00F46AF3 /* UserInfoEditAddressSubListViewController.swift */,
				BD4F03542E03F0EE00326B5D /* UserInfoEditAddressCityListViewController.swift */,
			);
			path = UserInfoEdit;
			sourceTree = "<group>";
		};
		BD43297B2DAD2ABD00188D9B /* Guide */ = {
			isa = PBXGroup;
			children = (
				BD4329792DAD2ABD00188D9B /* GuideViewController.swift */,
			);
			path = Guide;
			sourceTree = "<group>";
		};
		BD44476E2E2255B3008BB946 /* Util */ = {
			isa = PBXGroup;
			children = (
				BD44476D2E2255B3008BB946 /* MentionParser.swift */,
			);
			path = Util;
			sourceTree = "<group>";
		};
		BD4447712E22671E008BB946 /* Coordinator */ = {
			isa = PBXGroup;
			children = (
				BD4447702E22671E008BB946 /* AuthCoordinator.swift */,
			);
			path = Coordinator;
			sourceTree = "<group>";
		};
		BD4BA84C2E00FF03008D19C7 /* Common */ = {
			isa = PBXGroup;
			children = (
				BD4BA84B2E00FF03008D19C7 /* ImageCropPreviewController.swift */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		BD54967D2D92B53300F2F6C5 /* Modules */ = {
			isa = PBXGroup;
			children = (
				BDC2FC272E28CE9300444A56 /* Manager */,
				BDC7807F2DB9E3E200177DD8 /* Controller */,
				BD7189EA2E0CF4E000C781A6 /* Model */,
				BDC780812DB9E4AA00177DD8 /* View */,
				BD4447712E22671E008BB946 /* Coordinator */,
			);
			path = Modules;
			sourceTree = "<group>";
		};
		************************ /* PhoneBinding */ = {
			isa = PBXGroup;
			children = (
				BD5496802D92B57400F2F6C5 /* Controller */,
			);
			path = PhoneBinding;
			sourceTree = "<group>";
		};
		BD5496802D92B57400F2F6C5 /* Controller */ = {
			isa = PBXGroup;
			children = (
				BDB944082D8BB94500AAF5BD /* PhoneBindingViewController.swift */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		************************ /* Comment */ = {
			isa = PBXGroup;
			children = (
				BD54967B2D92982500F2F6C5 /* CommentHistoryViewController.swift */,
			);
			path = Comment;
			sourceTree = "<group>";
		};
		************************ /* Player */ = {
			isa = PBXGroup;
			children = (
				BDC2FD872E28ECED00444A56 /* CommentActionSheet.swift */,
				BD6560BE2E1CEE6D00084C6C /* NoteCarouselView.swift */,
				BDDC074C2E0E2916000D0903 /* PlayerTestVC.swift */,
				BDCB80E12D94F1F200725076 /* VideoCommentViewController.swift */,
				BDB5C8FD2D938FE400B2DCFE /* VideoDisplayCenterViewController.swift */,
				BD136CBE2E17AC0200CEED05 /* UIViewExtensions.swift */,
				BD136CBF2E17AC0200CEED05 /* VideoDisplayCenterUIComponents.swift */,
				BD136CC02E17AC0200CEED05 /* VideoDisplayCenterViewProtocols.swift */,
				BD136CC12E17AC0200CEED05 /* VideoPageUIComponents.swift */,
			);
			path = Player;
			sourceTree = "<group>";
		};
		BD5496942D92B7D400F2F6C5 /* Common */ = {
			isa = PBXGroup;
			children = (
				BD66ED652E0FEAE000E2C690 /* Events */,
				BD212AA62DD426A7004329BB /* WebViewJavascriptBridge */,
				BDC780892DBA0BC200177DD8 /* AliSDK */,
				BD0048382D96D020001BD612 /* TXSDK */,
				BD0048322D963637001BD612 /* NetworkManager */,
				BD5496962D92B7EE00F2F6C5 /* Constants */,
				BD5496952D92B7E700F2F6C5 /* Utils */,
				BD44476E2E2255B3008BB946 /* Util */,
				BDF701662E2E383D0065C7D8 /* Views */,
				BD43297B2DAD2ABD00188D9B /* Guide */,
				BD7223DB2D8A5D5700496130 /* Extensions */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		BD5496952D92B7E700F2F6C5 /* Utils */ = {
			isa = PBXGroup;
			children = (
				BDCEB3172E2B3354000CFB0F /* WeChatMiniProgramManager.swift */,
				BDB9440C2D8BC53D00AAF5BD /* UIWindowExtension.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		BD5496962D92B7EE00F2F6C5 /* Constants */ = {
			isa = PBXGroup;
			children = (
				BDE8E1FE2DE8693B0082E126 /* ShareSheetView.swift */,
			);
			path = Constants;
			sourceTree = "<group>";
		};
		BD5969E52D8952C8001194A9 /* Base */ = {
			isa = PBXGroup;
			children = (
				BDA9BA632DCA09E400621DCA /* NavigationController.swift */,
				BD7223DF2D8A62D500496130 /* CustomTabBarController.swift */,
				BDEF11102D895E5000583203 /* BaseViewController.swift */,
				BDB944002D8BA7D900AAF5BD /* HiddenNavController.swift */,
				BD4A45BB2DDC21A5000C64F9 /* occupations.json */,
				BD4A45BD2DDC5954000C64F9 /* university.json */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		************************ /* Friend */ = {
			isa = PBXGroup;
			children = (
				BD7223E12D8A66DD00496130 /* FriendViewController.swift */,
			);
			path = Friend;
			sourceTree = "<group>";
		};
		************************ /* Me */ = {
			isa = PBXGroup;
			children = (
				BD3CDAEF2E0C05D70017EEA3 /* BrowsingHistoryViewController.swift */,
				BDFC959F2DCC412D002D2FFC /* Personal */,
				BD91099A2D8D608900B66162 /* ContentManagementViewController.swift */,
				BD53A50F2D8958B00064BA5F /* MeViewController.swift */,
				BDB9440F2D8BED9700AAF5BD /* MyCollectionViewController.swift */,
				BD3B8FB92D8E4DFE005539BB /* RealNameAuthenticationResultViewController.swift */,
				BD8BC8552D91329400055118 /* CreativeCenterViewController.swift */,
				BDD4C9662D91451500478311 /* VideoDraftBoxViewController.swift */,
				BD431E052DB3366500D9765F /* UserSharingViewController.swift */,
				BD7D429F2DE3FDE7007986FE /* CreativeVideoItemCell.swift */,
			);
			path = Me;
			sourceTree = "<group>";
		};
		************************ /* Message */ = {
			isa = PBXGroup;
			children = (
				BD079B0E2DE588EB00FB2B57 /* Test */,
				BD53A5112D8958C10064BA5F /* MessageViewController.swift */,
				BD4CE3D42DE065B3004287BE /* AddedFollowershipNotificationViewController.swift */,
			);
			path = Message;
			sourceTree = "<group>";
		};
		BD5AC9F02DBC7E0C00350A6B /* Main */ = {
			isa = PBXGroup;
			children = (
				BD5AC9EE2DBC7D5F00350A6B /* ProductItemCell.swift */,
				BD5AC9F12DBC7E2500350A6B /* DiscoverProductCell.swift */,
				BD5AC9F32DBC7E6200350A6B /* VideoCollectionCell.swift */,
			);
			path = Main;
			sourceTree = "<group>";
		};
		BD666FD52DC1BF3C0081F608 /* Me */ = {
			isa = PBXGroup;
			children = (
				BD666FD62DC1BF8B0081F608 /* ProfileCell.swift */,
				BD666FD82DC1BFCA0081F608 /* FunctionsCell.swift */,
				BD666FDA2DC1BFE00081F608 /* ContentCell.swift */,
				BD666FDC2DC1C0080081F608 /* ContentItemCell.swift */,
				BDAAAA102DC99AE2007665C7 /* CleanupConfirmAlertView.swift */,
				BDAAAA122DC99CB7007665C7 /* CleanupFooterView.swift */,
				BD005AFB2DF2D29700F2A1AF /* ProfileHeaderView.swift */,
				BDAAAA142DC99D2C007665C7 /* SmallVideoItemCell.swift */,
			);
			path = Me;
			sourceTree = "<group>";
		};
		BD66ED652E0FEAE000E2C690 /* Events */ = {
			isa = PBXGroup;
			children = (
				BD66ED642E0FEAE000E2C690 /* ShareEvent.swift */,
			);
			path = Events;
			sourceTree = "<group>";
		};
		BD7223DB2D8A5D5700496130 /* Extensions */ = {
			isa = PBXGroup;
			children = (
				BDA9BA592DCA091A00621DCA /* CALayer+Ext.swift */,
				BDA9BA5A2DCA091A00621DCA /* String+Ext.swift */,
				BDA9BA5B2DCA091A00621DCA /* UITableView+Ext.swift */,
				BDA9BA5C2DCA091A00621DCA /* UIView+Ext.swift */,
				BDA9BA5D2DCA091A00621DCA /* UIWindow+Ext.swift */,
				BD7223DD2D8A602100496130 /* UIView+Gradient.swift */,
				BD7223DA2D8A5D5700496130 /* UIColor+Hex.swift */,
				BD133C062D9E75C500384522 /* UIViewController+Toast.swift */,
				BD4CE3D82DE09BA8004287BE /* UILabel+TapExtension.swift */,
				BD9A0DC72DE15E53007FED9A /* Color+App.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		************************ /* Setting */ = {
			isa = PBXGroup;
			children = (
				BDC7807D2DB9E09400177DD8 /* Security */,
				BDB5C8FA2D938CD400B2DCFE /* Controller */,
				BD402B862DFFEDA800F6F54E /* UserInfoEdit */,
			);
			path = Setting;
			sourceTree = "<group>";
		};
		BD8BC8482D9101C200055118 /* Follow */ = {
			isa = PBXGroup;
			children = (
				BD3B8FBB2D8EADC6005539BB /* FollowListViewController.swift */,
				BD8BC8492D9101DC00055118 /* FollowingContentViewController.swift */,
				BD8BC84B2D9101E400055118 /* FollowersContentViewController.swift */,
			);
			path = Follow;
			sourceTree = "<group>";
		};
		************************ /* Test */ = {
			isa = PBXGroup;
			children = (
				BD9017002DE72E65001201AF /* MapSearchResultViewController.swift */,
				BD9017012DE72E65001201AF /* MapSearchTestViewController.swift */,
				BD9017052DE72EE0001201AF /* MapTest.swift */,
			);
			path = Test;
			sourceTree = "<group>";
		};
		BD9CC70E2DF7CC880011BD68 /* Views */ = {
			isa = PBXGroup;
			children = (
				BD4828962DB8758E0068DBCC /* RecordGridView.swift */,
				BD9C86902DB5D21B00CED36A /* BeautyPanelView.swift */,
				BD212AAE2DD47756004329BB /* MusicListTableViewCell.swift */,
				BD212AB02DD49C10004329BB /* MusicPanelView.swift */,
				BDC7D5E72DE98DB100F75FCA /* AspectRatioSelectorView.swift */,
				BD4828942DB874230068DBCC /* RecordSettingsPopupView.swift */,
				BD10F3952DF7097B00A7E8A4 /* ModeCell.swift */,
				BDD363CD2DF194E300C0E56A /* TimerSelectorView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		BD9CC70F2DF7D2410011BD68 /* Model */ = {
			isa = PBXGroup;
			children = (
				BD9CC7102DF7D3BB0011BD68 /* MusicItem.swift */,
				BD9CC7122DF7D6090011BD68 /* AspectRatioOption.swift */,
				BD9CC7152DF7DC7F0011BD68 /* BeautyItem.swift */,
				BD9CC7172DF7DCAF0011BD68 /* DownloadState.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		BD9CC7142DF7D7EB0011BD68 /* Manager */ = {
			isa = PBXGroup;
			children = (
				BD10F3912DF6DCAD00A7E8A4 /* VolumeKeyCaptureManager.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		BD9CC7472DF83E5B0011BD68 /* test */ = {
			isa = PBXGroup;
			children = (
				BD900C292D9238FF00479E7D /* ModuleCentralViewController.swift */,
				BDD1DE772DA3A15A00E39244 /* VideoEditTestViewController.swift */,
				BD57CE772D9A9D8800052EE1 /* VideoSDKTestViewController.swift */,
			);
			path = test;
			sourceTree = "<group>";
		};
		BDA7BDB12DA7CCFA008DE990 /* helper */ = {
			isa = PBXGroup;
			children = (
				BDA7BDAF2DA7CCFA008DE990 /* TVCQuicConfigProxy.h */,
				BDA7BDB02DA7CCFA008DE990 /* TVCQuicConfigProxy.m */,
			);
			path = helper;
			sourceTree = "<group>";
		};
		BDA7BDB72DA7CCFA008DE990 /* include */ = {
			isa = PBXGroup;
			children = (
				BDA7BDB22DA7CCFA008DE990 /* IUploadResumeController.h */,
				BDA7BDB32DA7CCFA008DE990 /* TXUGCPublish.h */,
				BDA7BDB42DA7CCFA008DE990 /* TXUGCPublishListener.h */,
				BDA7BDB52DA7CCFA008DE990 /* TXUGCPublishTypeDef.h */,
				BDA7BDB62DA7CCFA008DE990 /* UploadResumeDefaultController.h */,
			);
			path = include;
			sourceTree = "<group>";
		};
		BDA7BDD32DA7CCFA008DE990 /* upload */ = {
			isa = PBXGroup;
			children = (
				BDA7BDB12DA7CCFA008DE990 /* helper */,
				BDA7BDB72DA7CCFA008DE990 /* include */,
				BDA7BDB82DA7CCFA008DE990 /* QuicClient.h */,
				BDA7BDB92DA7CCFA008DE990 /* QuicClient.m */,
				BDA7BDBA2DA7CCFA008DE990 /* TVCClient.h */,
				BDA7BDBB2DA7CCFA008DE990 /* TVCClient.m */,
				BDA7BDBC2DA7CCFA008DE990 /* TVCClientInner.h */,
				BDA7BDBD2DA7CCFA008DE990 /* TVCClientInner.m */,
				BDA7BDBE2DA7CCFA008DE990 /* TVCCommon.h */,
				BDA7BDBF2DA7CCFA008DE990 /* TVCCommon.m */,
				BDA7BDC02DA7CCFA008DE990 /* TVCConfig.h */,
				BDA7BDC12DA7CCFA008DE990 /* TVCConfig.m */,
				BDA7BDC22DA7CCFA008DE990 /* TVCCOSXMLEndPoint.h */,
				BDA7BDC32DA7CCFA008DE990 /* TVCCOSXMLEndPoint.m */,
				BDA7BDC42DA7CCFA008DE990 /* TVCHeader.h */,
				BDA7BDC52DA7CCFA008DE990 /* TVCHttpMessageURLProtocol.h */,
				BDA7BDC62DA7CCFA008DE990 /* TVCHttpMessageURLProtocol.m */,
				BDA7BDC72DA7CCFA008DE990 /* TVCLog.h */,
				BDA7BDC82DA7CCFA008DE990 /* TVCLog.m */,
				BDA7BDC92DA7CCFA008DE990 /* TVCReport.h */,
				BDA7BDCA2DA7CCFA008DE990 /* TVCReport.m */,
				BDA7BDCB2DA7CCFA008DE990 /* TVCUtils.h */,
				BDA7BDCC2DA7CCFA008DE990 /* TVCUtils.m */,
				BDA7BDCD2DA7CCFA008DE990 /* TXUGCPublish.m */,
				BDA7BDCE2DA7CCFA008DE990 /* TXUGCPublishOptCenter.h */,
				BDA7BDCF2DA7CCFA008DE990 /* TXUGCPublishOptCenter.m */,
				BDA7BDD02DA7CCFA008DE990 /* TXUGCPublishUtil.h */,
				BDA7BDD12DA7CCFA008DE990 /* TXUGCPublishUtil.m */,
				BDA7BDD22DA7CCFA008DE990 /* UploadResumeDefaultController.m */,
			);
			path = upload;
			sourceTree = "<group>";
		};
		BDB5C8F42D938B5100B2DCFE /* Edit */ = {
			isa = PBXGroup;
			children = (
				BD9CC7472DF83E5B0011BD68 /* test */,
				************************ /* VideoEditViewController.swift */,
				BD8CA0722DFA64E100040116 /* MusicSelectBar.swift */,
			);
			path = Edit;
			sourceTree = "<group>";
		};
		BDB5C8FA2D938CD400B2DCFE /* Controller */ = {
			isa = PBXGroup;
			children = (
				BDB9D1072E17E078004AB207 /* DeleteAccountViewController.swift */,
				************************ /* SettingViewController.swift */,
				BDB944022D8BAE1200AAF5BD /* AccountSettingViewController.swift */,
				BDB944042D8BB76100AAF5BD /* AccountPrivacyViewController.swift */,
				BDB944062D8BB87700AAF5BD /* DeviceManagementViewController.swift */,
				BDB9440A2D8BBD7100AAF5BD /* BlacklistManagerViewController.swift */,
				BDB944112D8BF04700AAF5BD /* NotificationSettingsViewController.swift */,
				BDAC748B2DAA608B0053D2A2 /* VerifyMobilePhoneNumberViewController.swift */,
				************************ /* SetNewPasswordViewController.swift */,
				BDA955212DB10FEA00344770 /* WebViewController.swift */,
				************************ /* AboutUsViewController.swift */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		BDC2FC272E28CE9300444A56 /* Manager */ = {
			isa = PBXGroup;
			children = (
				BDC2FC262E28CE9300444A56 /* LocationManager.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		BDC7807D2DB9E09400177DD8 /* Security */ = {
			isa = PBXGroup;
			children = (
				************************ /* PhoneBinding */,
			);
			path = Security;
			sourceTree = "<group>";
		};
		BDC7807F2DB9E3E200177DD8 /* Controller */ = {
			isa = PBXGroup;
			children = (
				BD4BA84C2E00FF03008D19C7 /* Common */,
				************************ /* Search */,
				************************ /* Test */,
				BD8BC8482D9101C200055118 /* Follow */,
				************************ /* Setting */,
				************************ /* Me */,
				************************ /* Comment */,
				BDC780842DB9E6ED00177DD8 /* Video */,
				BDC780802DB9E46100177DD8 /* Main */,
				************************ /* Message */,
				************************ /* Friend */,
				BDC780822DB9E4F800177DD8 /* Account */,
				BDC780832DB9E6D500177DD8 /* Commerce */,
				BD4CE3D12DE04EFB004287BE /* GoodsCheckstandViewController.swift */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		BDC780802DB9E46100177DD8 /* Main */ = {
			isa = PBXGroup;
			children = (
				BD1FEC232DD7668E00FC0AC8 /* Discover */,
				************************ /* HomeViewController.swift */,
				************************ /* DiscoverViewController.swift */,
				************************ /* StoreViewController.swift */,
				************************ /* AttentionViewController.swift */,
				************************ /* LeftMenuViewController.swift */,
				************************ /* UpgradePopupViewController.swift */,
				************************ /* PreferenceTagsViewController.swift */,
				************************ /* QRScanViewController.swift */,
				BDC780902DBA1EF100177DD8 /* SameCityViewController.swift */,
			);
			path = Main;
			sourceTree = "<group>";
		};
		BDC780812DB9E4AA00177DD8 /* View */ = {
			isa = PBXGroup;
			children = (
				BD0BCA492DF281E400F47F51 /* PopView */,
				BDE5E8AD2DD21B85006A913A /* FilterPanelView.swift */,
				BD20A8392DC1C7AC00562E78 /* Comment */,
				BD666FD52DC1BF3C0081F608 /* Me */,
				BD5AC9F02DBC7E0C00350A6B /* Main */,
				BD8BC84D2D9101EB00055118 /* FollowingCell.swift */,
				BD8BC84F2D9101F000055118 /* FollowersCell.swift */,
				BD8BC8512D9101F700055118 /* RecommendedUserCell.swift */,
				BDCB80E52D94F37A00725076 /* CommentCell.swift */,
				************************ /* VideoItemCell.swift */,
				BDFBD89D2E20A7C600F50798 /* CommentReplyCell.swift */,
				************************ /* ExpandRepliesCell.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		BDC780822DB9E4F800177DD8 /* Account */ = {
			isa = PBXGroup;
			children = (
				BDCB80DD2D94DDC100725076 /* LoginViewController.swift */,
				************************ /* LoginViewController2.0.swift */,
				BD4CE3D62DE09406004287BE /* LoginViewModel.swift */,
			);
			path = Account;
			sourceTree = "<group>";
		};
		BDC780832DB9E6D500177DD8 /* Commerce */ = {
			isa = PBXGroup;
			children = (
				************************ /* ApplyOpenShopWindowViewController.swift */,
				************************ /* VideoDeliveryCenterViewController.swift */,
				************************ /* VideoDeliveryCenterSearchViewCotroller.swift */,
			);
			path = Commerce;
			sourceTree = "<group>";
		};
		BDC780842DB9E6ED00177DD8 /* Video */ = {
			isa = PBXGroup;
			children = (
				BD7D42A12DE4876A007986FE /* LikedWorksViewController.swift */,
				BDC780862DB9E82B00177DD8 /* Upload */,
				BDC780852DB9E82600177DD8 /* Record */,
				BDB5C8F42D938B5100B2DCFE /* Edit */,
				************************ /* Player */,
			);
			path = Video;
			sourceTree = "<group>";
		};
		BDC780852DB9E82600177DD8 /* Record */ = {
			isa = PBXGroup;
			children = (
				BD9CC7142DF7D7EB0011BD68 /* Manager */,
				BD9CC70F2DF7D2410011BD68 /* Model */,
				BDC195312D97867600318532 /* VideoRecordViewController.swift */,
				BD9CC70E2DF7CC880011BD68 /* Views */,
				BD9CC7192DF7DDF40011BD68 /* VideoRecordViewModel.swift */,
			);
			path = Record;
			sourceTree = "<group>";
		};
		BDC780862DB9E82B00177DD8 /* Upload */ = {
			isa = PBXGroup;
			children = (
				BD1EFCAC2E0AB5710079E39B /* NoteEditingDetailsViewController.swift */,
				BD02D4572DFD096000D5D2C9 /* VideoUploadParameterManager.swift */,
				BD900C2B2D924B7E00479E7D /* VideoEditingDetailsViewController.swift */,
				BDD58DF22DFBD06000C26284 /* CategoryPickerPopupView.swift */,
			);
			path = Upload;
			sourceTree = "<group>";
		};
		BDC780892DBA0BC200177DD8 /* AliSDK */ = {
			isa = PBXGroup;
			children = (
				BDC7808A2DBA0BF800177DD8 /* ATAuthSDK.framework */,
				BDC7808C2DBA0C0B00177DD8 /* YTXMonitor.framework */,
				BDC7808D2DBA0C0B00177DD8 /* YTXOperators.framework */,
			);
			path = AliSDK;
			sourceTree = "<group>";
		};
		BDE5E8B02DD21C5A006A913A /* API */ = {
			isa = PBXGroup;
			children = (
				BDE5E8AF2DD21C5A006A913A /* FilterConfigItem.swift */,
			);
			path = API;
			sourceTree = "<group>";
		};
		BDE5E8B12DD21C5A006A913A /* Models */ = {
			isa = PBXGroup;
			children = (
				BDE5E8B02DD21C5A006A913A /* API */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		BDF701632E2DEDC60065C7D8 /* Components */ = {
			isa = PBXGroup;
			children = (
				BDF701622E2DEDC60065C7D8 /* InfoPopupView.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		BDF701662E2E383D0065C7D8 /* Views */ = {
			isa = PBXGroup;
			children = (
				BDF701652E2E383D0065C7D8 /* BackInterceptPopupView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		BDFC959F2DCC412D002D2FFC /* Personal */ = {
			isa = PBXGroup;
			children = (
				BDFC95F02DCC5A71002D2FFC /* Controller */,
				BDFC95EF2DCC59FC002D2FFC /* View */,
			);
			path = Personal;
			sourceTree = "<group>";
		};
		BDFC95EF2DCC59FC002D2FFC /* View */ = {
			isa = PBXGroup;
			children = (
				BDFC95A42DCC435E002D2FFC /* StatsView.swift */,
				BDFC95ED2DCC4BCB002D2FFC /* VideoViewCell.swift */,
				BDFC95C42DCC4A44002D2FFC /* PageContainScrollView.swift */,
				BDFC95C52DCC4A44002D2FFC /* PageHeaderView.swift */,
				BDFC95C62DCC4A44002D2FFC /* PageSegmentView.swift */,
				BDFC95C82DCC4A44002D2FFC /* RollingTools.swift */,
				BDFC95CA2DCC4A44002D2FFC /* VideoListController.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		BDFC95F02DCC5A71002D2FFC /* Controller */ = {
			isa = PBXGroup;
			children = (
				BDFC95A02DCC435E002D2FFC /* CollectionsListViewController.swift */,
				BDFC95A12DCC435E002D2FFC /* HomepageListProtocol.swift */,
				BD8BC8532D910D7C00055118 /* PersonalHomepageViewController.swift */,
				BD695C492DF04CA8004A2F0F /* CommonAlertView.swift */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		F3B695FA91CB6D7F488D0BD2 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				BD8751272DE8356100C75E45 /* WechatOpenSDK-XCFramework.xcframework */,
				BDC780872DBA0B8100177DD8 /* Network.framework */,
				BDC195342D978D6800318532 /* CoreMotion.framework */,
				BDC1952F2D97836A00318532 /* CoreTelephony.framework */,
				BD0048512D96D1BD001BD612 /* CoreServices.framework */,
				BD00484F2D96D1B6001BD612 /* OpenAL.framework */,
				BD00484D2D96D1B0001BD612 /* GLKit.framework */,
				BD00484B2D96D1AB001BD612 /* ReplayKit.framework */,
				BD0048492D96D1A6001BD612 /* VideoToolbox.framework */,
				BD0048472D96D19D001BD612 /* MetalKit.framework */,
				BD0048452D96D192001BD612 /* libsqlite3.tbd */,
				BD0048432D96D188001BD612 /* libc++.tbd */,
				BD0048412D96D17D001BD612 /* SystemConfiguration.framework */,
				BD00483F2D96D174001BD612 /* Accelerate.framework */,
				DC01EA41A40410852670D7CA /* Pods_Shuxiaoqi.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		FF5FFA0D3091F9CE1CD22CE3 /* Pods */ = {
			isa = PBXGroup;
			children = (
				A3CF7779FFF197B3D4A29621 /* Pods-Shuxiaoqi.debug.xcconfig */,
				C3E5D272A67060B18C2EBECB /* Pods-Shuxiaoqi.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		04ABF0F42D89040100E85541 /* Shuxiaoqi */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 04ABF11F2D89040400E85541 /* Build configuration list for PBXNativeTarget "Shuxiaoqi" */;
			buildPhases = (
				5A25C062A54B201BE5C24C6C /* [CP] Check Pods Manifest.lock */,
				04ABF0F12D89040100E85541 /* Sources */,
				04ABF0F22D89040100E85541 /* Frameworks */,
				04ABF0F32D89040100E85541 /* Resources */,
				BDC1953C2D9795B400318532 /* Embed Frameworks */,
				431F254C54C40F3C81DA76B0 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				************************ /* Search */,
				BD7189EA2E0CF4E000C781A6 /* Model */,
			);
			name = Shuxiaoqi;
			productName = Shuxiaoqi;
			productReference = 04ABF0F52D89040100E85541 /* Shuxiaoqi.app */;
			productType = "com.apple.product-type.application";
		};
		04ABF10A2D89040400E85541 /* ShuxiaoqiTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 04ABF1222D89040400E85541 /* Build configuration list for PBXNativeTarget "ShuxiaoqiTests" */;
			buildPhases = (
				04ABF1072D89040400E85541 /* Sources */,
				04ABF1082D89040400E85541 /* Frameworks */,
				04ABF1092D89040400E85541 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				04ABF10D2D89040400E85541 /* PBXTargetDependency */,
			);
			name = ShuxiaoqiTests;
			productName = ShuxiaoqiTests;
			productReference = 04ABF10B2D89040400E85541 /* ShuxiaoqiTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		04ABF1142D89040400E85541 /* ShuxiaoqiUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 04ABF1252D89040400E85541 /* Build configuration list for PBXNativeTarget "ShuxiaoqiUITests" */;
			buildPhases = (
				04ABF1112D89040400E85541 /* Sources */,
				04ABF1122D89040400E85541 /* Frameworks */,
				04ABF1132D89040400E85541 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				04ABF1172D89040400E85541 /* PBXTargetDependency */,
			);
			name = ShuxiaoqiUITests;
			productName = ShuxiaoqiUITests;
			productReference = 04ABF1152D89040400E85541 /* ShuxiaoqiUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		04ABF0ED2D89040100E85541 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					04ABF0F42D89040100E85541 = {
						CreatedOnToolsVersion = 15.4;
					};
					04ABF10A2D89040400E85541 = {
						CreatedOnToolsVersion = 15.4;
						TestTargetID = 04ABF0F42D89040100E85541;
					};
					04ABF1142D89040400E85541 = {
						CreatedOnToolsVersion = 15.4;
						TestTargetID = 04ABF0F42D89040100E85541;
					};
				};
			};
			buildConfigurationList = 04ABF0F02D89040100E85541 /* Build configuration list for PBXProject "Shuxiaoqi" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 04ABF0EC2D89040100E85541;
			productRefGroup = 04ABF0F62D89040100E85541 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				04ABF0F42D89040100E85541 /* Shuxiaoqi */,
				04ABF10A2D89040400E85541 /* ShuxiaoqiTests */,
				04ABF1142D89040400E85541 /* ShuxiaoqiUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		04ABF0F32D89040100E85541 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BD91C0002DA4D3FA002003A4 /* ap002.mp3 in Resources */,
				BD4A45BE2DDC5954000C64F9 /* university.json in Resources */,
				BD91C0012DA4D3FA002003A4 /* ap001.mp3 in Resources */,
				BD4A45BC2DDC21A5000C64F9 /* occupations.json in Resources */,
				04ABF1022D89040400E85541 /* Assets.xcassets in Resources */,
				BDD56B302D896A7000F805C3 /* LaunchScreenV2.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04ABF1092D89040400E85541 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04ABF1132D89040400E85541 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		431F254C54C40F3C81DA76B0 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Shuxiaoqi/Pods-Shuxiaoqi-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Shuxiaoqi/Pods-Shuxiaoqi-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Shuxiaoqi/Pods-Shuxiaoqi-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		5A25C062A54B201BE5C24C6C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Shuxiaoqi-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		04ABF0F12D89040100E85541 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BDE5E8B22DD21C5A006A913A /* FilterConfigItem.swift in Sources */,
				BD0BCA4F2DF2913C00F47F51 /* FollowMessageListViewController.swift in Sources */,
				BDB9440B2D8BBD7100AAF5BD /* BlacklistManagerViewController.swift in Sources */,
				BD53A5102D8958B00064BA5F /* MeViewController.swift in Sources */,
				BDCB80E22D94F1F200725076 /* VideoCommentViewController.swift in Sources */,
				BD91099D2D8D910A00B66162 /* ApplyOpenShopWindowViewController.swift in Sources */,
				BD8BC8542D910D7C00055118 /* PersonalHomepageViewController.swift in Sources */,
				BD8BC8562D91329400055118 /* CreativeCenterViewController.swift in Sources */,
				BD4A45CD2DDC8553000C64F9 /* UserInfoEditAddressViewController.swift in Sources */,
				BD57CE782D9A9D8800052EE1 /* VideoSDKTestViewController.swift in Sources */,
				BD9A0DCA2DE16FBE007FED9A /* VideoHorizontalListCell.swift in Sources */,
				BD8BC84E2D9101EB00055118 /* FollowingCell.swift in Sources */,
				BD4CE3D72DE09406004287BE /* LoginViewModel.swift in Sources */,
				************************ /* SetNewPasswordViewController.swift in Sources */,
				BDD4C9672D91451500478311 /* VideoDraftBoxViewController.swift in Sources */,
				BDA955222DB10FEA00344770 /* WebViewController.swift in Sources */,
				************************ /* APIRouter.swift in Sources */,
				BDB944052D8BB76100AAF5BD /* AccountPrivacyViewController.swift in Sources */,
				BD9C86912DB5D21B00CED36A /* BeautyPanelView.swift in Sources */,
				************************ /* VideoUploadParameterManager.swift in Sources */,
				************************ /* MentionParser.swift in Sources */,
				************************ /* ShareEvent.swift in Sources */,
				************************ /* ContentManagementViewController.swift in Sources */,
				BD7D42A22DE4876A007986FE /* LikedWorksViewController.swift in Sources */,
				BDFC95EE2DCC4BCB002D2FFC /* VideoViewCell.swift in Sources */,
				************************ /* NetworkManager.swift in Sources */,
				BD1FEC322DD769A700FC0AC8 /* CategoryPopupCell.swift in Sources */,
				************************ /* RecordSettingsPopupView.swift in Sources */,
				BD5AC9F42DBC7E6200350A6B /* VideoCollectionCell.swift in Sources */,
				************************ /* CoinTransactionRecordViewController.swift in Sources */,
				BDD58DF12DFBCFD600C26284 /* YearPickerPopupView.swift in Sources */,
				BDA7BDD42DA7CCFA008DE990 /* TXUGCPublishOptCenter.m in Sources */,
				BDA7BDD52DA7CCFA008DE990 /* TXUGCPublish.m in Sources */,
				BD92C4632DC0B7240039AAC3 /* AboutUsViewController.swift in Sources */,
				BDA7BDD62DA7CCFA008DE990 /* TVCHttpMessageURLProtocol.m in Sources */,
				BDA7BDD72DA7CCFA008DE990 /* TVCReport.m in Sources */,
				BDC2FD882E28ECED00444A56 /* CommentActionSheet.swift in Sources */,
				BD4A45D12DDC8F5E000C64F9 /* UserInfoEditSchoolList.swift in Sources */,
				BDA7BDD82DA7CCFA008DE990 /* TVCConfig.m in Sources */,
				BDA7BDD92DA7CCFA008DE990 /* UploadResumeDefaultController.m in Sources */,
				BDA7BDDA2DA7CCFA008DE990 /* TVCCommon.m in Sources */,
				BD4CE3D22DE04EFB004287BE /* GoodsCheckstandViewController.swift in Sources */,
				BDC2FC282E28CE9300444A56 /* LocationManager.swift in Sources */,
				BDA7BDDB2DA7CCFA008DE990 /* QuicClient.m in Sources */,
				BDA7BDDC2DA7CCFA008DE990 /* TVCCOSXMLEndPoint.m in Sources */,
				BDA7BDDD2DA7CCFA008DE990 /* TVCLog.m in Sources */,
				BDA7BDDE2DA7CCFA008DE990 /* TVCClient.m in Sources */,
				BD5AC9EF2DBC7D5F00350A6B /* ProductItemCell.swift in Sources */,
				BDA7BDDF2DA7CCFA008DE990 /* TXUGCPublishUtil.m in Sources */,
				BDA7BDE02DA7CCFA008DE990 /* TVCQuicConfigProxy.m in Sources */,
				BDAAAA152DC99D2C007665C7 /* SmallVideoItemCell.swift in Sources */,
				BD8CA0732DFA64E100040116 /* MusicSelectBar.swift in Sources */,
				BD212AB12DD49C10004329BB /* MusicPanelView.swift in Sources */,
				BDF701672E2E383D0065C7D8 /* BackInterceptPopupView.swift in Sources */,
				BD05791A2DEED84C008C14A2 /* InteractiveInformationViewController.swift in Sources */,
				BDA7BDE12DA7CCFA008DE990 /* TVCUtils.m in Sources */,
				BDA7BDE22DA7CCFA008DE990 /* TVCClientInner.m in Sources */,
				BD9017062DE72EE0001201AF /* MapTest.swift in Sources */,
				BDAAAA112DC99AE2007665C7 /* CleanupConfirmAlertView.swift in Sources */,
				BDC195322D97867600318532 /* VideoRecordViewController.swift in Sources */,
				BD0048362D963637001BD612 /* APIService.swift in Sources */,
				BD7D42A02DE3FDE7007986FE /* CreativeVideoItemCell.swift in Sources */,
				BD0048372D963637001BD612 /* APIResponse.swift in Sources */,
				BD91098B2D8CF68100B66162 /* VideoDeliveryCenterViewController.swift in Sources */,
				BD695C4A2DF04CA8004A2F0F /* CommonAlertView.swift in Sources */,
				BD54968C2D92B6AA00F2F6C5 /* ReplyCell.swift in Sources */,
				BD7223DE2D8A602100496130 /* UIView+Gradient.swift in Sources */,
				BDB944092D8BB94500AAF5BD /* PhoneBindingViewController.swift in Sources */,
				BDAAAA132DC99CB7007665C7 /* CleanupFooterView.swift in Sources */,
				BD1EFCAE2E0AB5710079E39B /* NoteEditingDetailsViewController.swift in Sources */,
				BDA9BA642DCA09E400621DCA /* NavigationController.swift in Sources */,
				BDAC748C2DAA608B0053D2A2 /* VerifyMobilePhoneNumberViewController.swift in Sources */,
				BD4CE3D92DE09BA8004287BE /* UILabel+TapExtension.swift in Sources */,
				BD1FEC282DD7681800FC0AC8 /* RecommendListViewController.swift in Sources */,
				BDD58DF32DFBD06000C26284 /* CategoryPickerPopupView.swift in Sources */,
				BD7223E02D8A62D500496130 /* CustomTabBarController.swift in Sources */,
				BDCB80DE2D94DDC100725076 /* LoginViewController.swift in Sources */,
				BD68D4F62D9CCCEC00C3E9BF /* VideoItemCell.swift in Sources */,
				BDBFCEB62DDB347800278842 /* UserInfoEditGenderViewController.swift in Sources */,
				BD431E062DB3366500D9765F /* UserSharingViewController.swift in Sources */,
				BD7223DC2D8A5D5700496130 /* UIColor+Hex.swift in Sources */,
				BD8BC84A2D9101DC00055118 /* FollowingContentViewController.swift in Sources */,
				BDB9440E2D8BC53D00AAF5BD /* UIWindowExtension.swift in Sources */,
				BD1FEC262DD767E200FC0AC8 /* SectionHeaderView.swift in Sources */,
				BDB944102D8BED9700AAF5BD /* MyCollectionViewController.swift in Sources */,
				BD900C2C2D924B7E00479E7D /* VideoEditingDetailsViewController.swift in Sources */,
				BDFC95CE2DCC4A44002D2FFC /* VideoListController.swift in Sources */,
				BDBFCEB82DDB4CE800278842 /* UserInfoEditSignatureViewController.swift in Sources */,
				BDFC95D02DCC4A44002D2FFC /* PageHeaderView.swift in Sources */,
				BDFC95D22DCC4A44002D2FFC /* PageContainScrollView.swift in Sources */,
				BD4BA84D2E00FF03008D19C7 /* ImageCropPreviewController.swift in Sources */,
				BDFC95D32DCC4A44002D2FFC /* RollingTools.swift in Sources */,
				BDFC95D42DCC4A44002D2FFC /* PageSegmentView.swift in Sources */,
				BD212AAF2DD47756004329BB /* MusicListTableViewCell.swift in Sources */,
				BD005AFC2DF2D29700F2A1AF /* ProfileHeaderView.swift in Sources */,
				BD9445F32DCF1DD90006F038 /* LoginViewController2.0.swift in Sources */,
				BD133C092D9E804F00384522 /* APIRequest.swift in Sources */,
				BD7223E22D8A66DD00496130 /* FriendViewController.swift in Sources */,
				BD3710DD2E25105100267160 /* ExpandRepliesCell.swift in Sources */,
				BD8BC84C2D9101E400055118 /* FollowersContentViewController.swift in Sources */,
				BD49D2CF2DB2375100652DAE /* QRScanViewController.swift in Sources */,
				BD4CE3D52DE065B3004287BE /* AddedFollowershipNotificationViewController.swift in Sources */,
				BD0BCA4B2DF281F900F47F51 /* NotificationPopSelector.swift in Sources */,
				BD3B8FBC2D8EADC6005539BB /* FollowListViewController.swift in Sources */,
				BD83D20A2D8AEAF4002C65BC /* SettingViewController.swift in Sources */,
				BDB944072D8BB87700AAF5BD /* DeviceManagementViewController.swift in Sources */,
				BD83D2052D8A949B002C65BC /* AttentionViewController.swift in Sources */,
				BDC9A0732DD5ED0C001ED626 /* UserInfoEditNameViewController.swift in Sources */,
				BD6560BF2E1CEE6D00084C6C /* NoteCarouselView.swift in Sources */,
				BD3B8FBA2D8E4DFE005539BB /* RealNameAuthenticationResultViewController.swift in Sources */,
				BDB944012D8BA7D900AAF5BD /* HiddenNavController.swift in Sources */,
				BD666FD72DC1BF8B0081F608 /* ProfileCell.swift in Sources */,
				04ABF0F92D89040100E85541 /* AppDelegate.swift in Sources */,
				BDC9A0712DD5D636001ED626 /* UserInformationEditingPage.swift in Sources */,
				BD4447722E22671E008BB946 /* AuthCoordinator.swift in Sources */,
				BD666FDB2DC1BFE00081F608 /* ContentCell.swift in Sources */,
				BD1FEC2E2DD768FF00FC0AC8 /* CategorySelectionPopupView.swift in Sources */,
				BD9270D32DDDC7F700F46AF3 /* UserInfoEditAddressList.swift in Sources */,
				BD9A0DC82DE15E53007FED9A /* Color+App.swift in Sources */,
				BD9270D92DDDFC1A00F46AF3 /* UserInfoEditAddressSubListViewController.swift in Sources */,
				BDB5C8FE2D938FE400B2DCFE /* VideoDisplayCenterViewController.swift in Sources */,
				BD9CC71A2DF7DDF40011BD68 /* VideoRecordViewModel.swift in Sources */,
				BD5AC9F22DBC7E2500350A6B /* DiscoverProductCell.swift in Sources */,
				04ABF0FB2D89040100E85541 /* SceneDelegate.swift in Sources */,
				************************ /* MessageViewController.swift in Sources */,
				BDC780912DBA1EF100177DD8 /* SameCityViewController.swift in Sources */,
				BD5AC9F82DBCD39100350A6B /* RASManager.swift in Sources */,
				************************ /* VideoDeliveryCenterSearchViewCotroller.swift in Sources */,
				BDB9D1082E17E078004AB207 /* DeleteAccountViewController.swift in Sources */,
				BDA9BA5E2DCA091A00621DCA /* UIView+Ext.swift in Sources */,
				************************ /* MapSearchTestViewController.swift in Sources */,
				************************ /* MapSearchResultViewController.swift in Sources */,
				BDA9BA5F2DCA091A00621DCA /* CALayer+Ext.swift in Sources */,
				BDA9BA602DCA091A00621DCA /* String+Ext.swift in Sources */,
				BD4F03552E03F0EE00326B5D /* UserInfoEditAddressCityListViewController.swift in Sources */,
				BDA9BA612DCA091A00621DCA /* UIWindow+Ext.swift in Sources */,
				BD9CC7132DF7D6090011BD68 /* AspectRatioOption.swift in Sources */,
				BDF701642E2DEDC60065C7D8 /* InfoPopupView.swift in Sources */,
				BDA9BA622DCA091A00621DCA /* UITableView+Ext.swift in Sources */,
				************************ /* CommissionChangeDetailViewController.swift in Sources */,
				BD8BC8502D9101F000055118 /* FollowersCell.swift in Sources */,
				BDE8E1FF2DE8693B0082E126 /* ShareSheetView.swift in Sources */,
				BD4A45CF2DDC8636000C64F9 /* UserInfoEditSchoolViewController.swift in Sources */,
				BD54968A2D92B6A400F2F6C5 /* CommentHistoryCell.swift in Sources */,
				BD75A0902DAFB3BA006AEF62 /* UpgradePopupViewController.swift in Sources */,
				BDFBD89E2E20A7C600F50798 /* CommentReplyCell.swift in Sources */,
				BD75A0922DAFBBB0006AEF62 /* PreferenceTagsViewController.swift in Sources */,
				BDCB80E62D94F37A00725076 /* CommentCell.swift in Sources */,
				BD9CC7112DF7D3BB0011BD68 /* MusicItem.swift in Sources */,
				BD43297C2DAD2ABD00188D9B /* GuideViewController.swift in Sources */,
				BD1FEC2A2DD7687200FC0AC8 /* NormalListViewController.swift in Sources */,
				BD133C072D9E75C500384522 /* UIViewController+Toast.swift in Sources */,
				BDC9A0752DD5F599001ED626 /* UserInfoEditNumViewController.swift in Sources */,
				BDBFCEBA2DDB5D6100278842 /* UserInfoEditOccupationViewController.swift in Sources */,
				BD0BCA522DF2943A00F47F51 /* FollowMessageCell.swift in Sources */,
				BD0BCA532DF2943A00F47F51 /* FollowMessageListViewModel.swift in Sources */,
				BD1FEC2C2DD768D100FC0AC8 /* DiscoverSearchResultCell.swift in Sources */,
				************************ /* ModeCell.swift in Sources */,
				BDCEB3182E2B3354000CFB0F /* WeChatMiniProgramManager.swift in Sources */,
				************************ /* RecordGridView.swift in Sources */,
				BDB944032D8BAE1200AAF5BD /* AccountSettingViewController.swift in Sources */,
				BDE5E8AE2DD21B85006A913A /* FilterPanelView.swift in Sources */,
				************************ /* StoreViewController.swift in Sources */,
				BDD1DE782DA3A15A00E39244 /* VideoEditTestViewController.swift in Sources */,
				BD3CDAF02E0C05D70017EEA3 /* BrowsingHistoryViewController.swift in Sources */,
				BD9CC7182DF7DCAF0011BD68 /* DownloadState.swift in Sources */,
				BD8BC8522D9101F700055118 /* RecommendedUserCell.swift in Sources */,
				BDFC95A62DCC435E002D2FFC /* StatsView.swift in Sources */,
				************************ /* VolumeKeyCaptureManager.swift in Sources */,
				************************ /* DeviceUtils.swift in Sources */,
				BDAE9B372DDDB0BC00AB0C50 /* UserInfoEditBirthdayViewController.swift in Sources */,
				BDFC95A82DCC435E002D2FFC /* CollectionsListViewController.swift in Sources */,
				BDFC95AA2DCC435E002D2FFC /* HomepageListProtocol.swift in Sources */,
				BDC7D5E82DE98DB100F75FCA /* AspectRatioSelectorView.swift in Sources */,
				BD83D2012D8A78A1002C65BC /* DiscoverViewController.swift in Sources */,
				BDB944122D8BF04700AAF5BD /* NotificationSettingsViewController.swift in Sources */,
				BDEF11112D895E5000583203 /* BaseViewController.swift in Sources */,
				BD9CC7162DF7DC7F0011BD68 /* BeautyItem.swift in Sources */,
				BD212AA72DD426A7004329BB /* WebViewJavascriptBridge.m in Sources */,
				BD0BCA4D2DF2870300F47F51 /* VisiblePopSelector.swift in Sources */,
				BD212AA82DD426A7004329BB /* WebViewJavascriptBridgeBase.m in Sources */,
				BD212AA92DD426A7004329BB /* WebViewJavascriptBridge_JS.m in Sources */,
				BD1FEC242DD7668E00FC0AC8 /* IconMenuCell.swift in Sources */,
				BD212AAA2DD426A7004329BB /* WKWebViewJavascriptBridge.m in Sources */,
				BD07B77F2D97E8F9003F8A24 /* VideoEditViewController.swift in Sources */,
				BDD363CE2DF194E300C0E56A /* TimerSelectorView.swift in Sources */,
				BD83D2072D8AC670002C65BC /* LeftMenuViewController.swift in Sources */,
				BD900C2A2D9238FF00479E7D /* ModuleCentralViewController.swift in Sources */,
				BD718A492E0D377300C781A6 /* APIManager+Search.swift in Sources */,
				BD136CC22E17AC0200CEED05 /* VideoDisplayCenterUIComponents.swift in Sources */,
				BD136CC32E17AC0200CEED05 /* VideoDisplayCenterViewProtocols.swift in Sources */,
				BD136CC42E17AC0200CEED05 /* VideoPageUIComponents.swift in Sources */,
				BD136CC52E17AC0200CEED05 /* UIViewExtensions.swift in Sources */,
				BD133C0D2D9E806900384522 /* APIManager.swift in Sources */,
				BDDC074D2E0E2916000D0903 /* PlayerTestVC.swift in Sources */,
				BD5969EB2D895338001194A9 /* HomeViewController.swift in Sources */,
				BD666FDD2DC1C0080081F608 /* ContentItemCell.swift in Sources */,
				BD666FD92DC1BFCA0081F608 /* FunctionsCell.swift in Sources */,
				BD54967C2D92982500F2F6C5 /* CommentHistoryViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04ABF1072D89040400E85541 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				04ABF1102D89040400E85541 /* ShuxiaoqiTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		04ABF1112D89040400E85541 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				04ABF11C2D89040400E85541 /* ShuxiaoqiUITestsLaunchTests.swift in Sources */,
				04ABF11A2D89040400E85541 /* ShuxiaoqiUITests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		04ABF10D2D89040400E85541 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 04ABF0F42D89040100E85541 /* Shuxiaoqi */;
			targetProxy = 04ABF10C2D89040400E85541 /* PBXContainerItemProxy */;
		};
		04ABF1172D89040400E85541 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 04ABF0F42D89040100E85541 /* Shuxiaoqi */;
			targetProxy = 04ABF1162D89040400E85541 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		BDD56B2E2D896A7000F805C3 /* LaunchScreenV2.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				BDD56B2F2D896A7000F805C3 /* Base */,
			);
			name = LaunchScreenV2.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		04ABF11D2D89040400E85541 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_NSMainStoryboardFile = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		04ABF11E2D89040400E85541 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_NSMainStoryboardFile = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		04ABF1202D89040400E85541 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A3CF7779FFF197B3D4A29621 /* Pods-Shuxiaoqi.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = Shuxiaoqi/Shuxiaoqi.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 46WWM958T8;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GYSide\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/JXSegmentedView\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/LookinServer\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SwiftDate\"",
					"$(PROJECT_DIR)/Shuxiaoqi/Common/TXSDK",
					"$(PROJECT_DIR)/Shuxiaoqi/Common/AliSDK",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Shuxiaoqi/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "树小柒";
				INFOPLIST_KEY_NSAppleMusicUsageDescription = "树小柒需要访问您的媒体库权限以获取音乐，不允许则无法添加音乐";
				INFOPLIST_KEY_NSCameraUsageDescription = "需要访问您的相机以拍摄照片或视频";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "需要获取您的位置信息以提供精准服务";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "需要获取您的位置信息以提供精准服务";
				INFOPLIST_KEY_NSMainStoryboardFile = "";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "需要访问您的麦克风以录制视频";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "树小柒需要访问您的相册权限，开启后才能保存编辑的文件";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问您的相册以选择和编辑照片";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreenV2;
				INFOPLIST_KEY_UIStatusBarStyle = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					"\"Combine\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GYSide\"",
					"-framework",
					"\"JXSegmentedView\"",
					"-framework",
					"\"LookinServer\"",
					"-framework",
					"\"MBProgressHUD\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"SwiftDate\"",
					"-framework",
					"\"UIKit\"",
					"-ObjC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.gzyoushu.sxq;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Shuxiaoqi/Shuxiaoqi-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		04ABF1212D89040400E85541 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C3E5D272A67060B18C2EBECB /* Pods-Shuxiaoqi.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = Shuxiaoqi/Shuxiaoqi.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 46WWM958T8;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GYSide\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/JXSegmentedView\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/MBProgressHUD\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SwiftDate\"",
					"$(PROJECT_DIR)/Shuxiaoqi/Common/TXSDK",
					"$(PROJECT_DIR)/Shuxiaoqi/Common/AliSDK",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Shuxiaoqi/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "树小柒";
				INFOPLIST_KEY_NSAppleMusicUsageDescription = "树小柒需要访问您的媒体库权限以获取音乐，不允许则无法添加音乐";
				INFOPLIST_KEY_NSCameraUsageDescription = "需要访问您的相机以拍摄照片或视频";
				INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription = "需要获取您的位置信息以提供精准服务";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "需要获取您的位置信息以提供精准服务";
				INFOPLIST_KEY_NSMainStoryboardFile = "";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "需要访问您的麦克风以录制视频";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "树小柒需要访问您的相册权限，开启后才能保存编辑的文件";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问您的相册以选择和编辑照片";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreenV2;
				INFOPLIST_KEY_UIStatusBarStyle = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-framework",
					"\"Combine\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"GYSide\"",
					"-framework",
					"\"JXSegmentedView\"",
					"-framework",
					"\"MBProgressHUD\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"SwiftDate\"",
					"-framework",
					"\"UIKit\"",
					"-ObjC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.gzyoushu.sxq;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Shuxiaoqi/Shuxiaoqi-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		04ABF1232D89040400E85541 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.mt.ShuxiaoqiTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Shuxiaoqi.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Shuxiaoqi";
			};
			name = Debug;
		};
		04ABF1242D89040400E85541 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.mt.ShuxiaoqiTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Shuxiaoqi.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Shuxiaoqi";
			};
			name = Release;
		};
		04ABF1262D89040400E85541 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JXJZNTUXTP;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.mt.ShuxiaoqiUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Shuxiaoqi;
			};
			name = Debug;
		};
		04ABF1272D89040400E85541 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JXJZNTUXTP;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.mt.ShuxiaoqiUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Shuxiaoqi;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		04ABF0F02D89040100E85541 /* Build configuration list for PBXProject "Shuxiaoqi" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04ABF11D2D89040400E85541 /* Debug */,
				04ABF11E2D89040400E85541 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		04ABF11F2D89040400E85541 /* Build configuration list for PBXNativeTarget "Shuxiaoqi" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04ABF1202D89040400E85541 /* Debug */,
				04ABF1212D89040400E85541 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		04ABF1222D89040400E85541 /* Build configuration list for PBXNativeTarget "ShuxiaoqiTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04ABF1232D89040400E85541 /* Debug */,
				04ABF1242D89040400E85541 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		04ABF1252D89040400E85541 /* Build configuration list for PBXNativeTarget "ShuxiaoqiUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04ABF1262D89040400E85541 /* Debug */,
				04ABF1272D89040400E85541 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 04ABF0ED2D89040100E85541 /* Project object */;
}
